{"version": 3, "file": "user-store.js", "sources": ["utils/user-store.js"], "sourcesContent": ["// 用户状态管理\nimport { reactive, computed } from 'vue';\nimport wechatAuth, { checkLogin, logout } from './wechat-auth.js';\nimport { isDebug } from '@/config/env.js';\n\n/**\n * 用户状态枚举\n */\nexport const USER_STATUS = {\n  GUEST: 'guest',           // 游客状态\n  LOGGED_IN: 'logged_in',   // 已登录\n  VERIFIED: 'verified',     // 已实名认证\n  DISABLED: 'disabled'      // 账户被禁用\n};\n\n/**\n * 用户数据结构\n */\nconst defaultUserData = {\n  // 基础信息\n  userId: null,\n  username: '',\n  nickname: '',\n  avatar: '',\n  phone: '',\n  email: '',\n  \n  // 微信信息\n  openid: '',\n  unionid: '',\n  wechatInfo: {\n    nickName: '',\n    avatarUrl: '',\n    gender: 0,\n    country: '',\n    province: '',\n    city: '',\n    language: ''\n  },\n  \n  // 认证信息\n  isVerified: false,\n  realName: '',\n  idCard: '',\n  verifyTime: null,\n  \n  // 状态信息\n  status: USER_STATUS.GUEST,\n  loginTime: null,\n  lastActiveTime: null,\n  \n  // 权限信息\n  permissions: [],\n  roles: []\n};\n\n/**\n * 用户状态管理\n */\nclass UserStore {\n  constructor() {\n    // 响应式用户数据\n    this.state = reactive({\n      ...defaultUserData,\n      isLoading: false,\n      isInitialized: false\n    });\n    \n    // 初始化\n    this.init();\n  }\n\n  /**\n   * 初始化用户状态\n   */\n  async init() {\n    try {\n      this.state.isLoading = true;\n      \n      // 检查登录状态\n      const loginStatus = await checkLogin();\n      \n      if (loginStatus.isLogin) {\n        // 从本地存储恢复用户信息\n        await this.restoreUserInfo();\n        \n        // 验证token有效性\n        await this.validateToken();\n      } else {\n        // 清除过期信息\n        this.clearUserInfo();\n      }\n      \n      this.state.isInitialized = true;\n      \n      if (isDebug()) {\n        console.log('用户状态初始化完成:', this.state);\n      }\n    } catch (error) {\n      console.error('用户状态初始化失败:', error);\n      this.clearUserInfo();\n    } finally {\n      this.state.isLoading = false;\n    }\n  }\n\n  /**\n   * 从本地存储恢复用户信息\n   */\n  async restoreUserInfo() {\n    try {\n      const userInfo = uni.getStorageSync('userInfo');\n      const wechatUserInfo = uni.getStorageSync('wechat_userInfo');\n      const openid = uni.getStorageSync('openid');\n      const unionid = uni.getStorageSync('unionid');\n      \n      if (userInfo) {\n        Object.assign(this.state, userInfo);\n        this.state.status = USER_STATUS.LOGGED_IN;\n      }\n      \n      if (wechatUserInfo) {\n        this.state.wechatInfo = wechatUserInfo;\n        // 如果没有设置头像，使用微信头像\n        if (!this.state.avatar && wechatUserInfo.avatarUrl) {\n          this.state.avatar = wechatUserInfo.avatarUrl;\n        }\n        // 如果没有设置昵称，使用微信昵称\n        if (!this.state.nickname && wechatUserInfo.nickName) {\n          this.state.nickname = wechatUserInfo.nickName;\n        }\n      }\n      \n      if (openid) {\n        this.state.openid = openid;\n      }\n      \n      if (unionid) {\n        this.state.unionid = unionid;\n      }\n      \n    } catch (error) {\n      console.error('恢复用户信息失败:', error);\n    }\n  }\n\n  /**\n   * 验证token有效性\n   */\n  async validateToken() {\n    try {\n      // 这里可以调用后端API验证token\n      // 暂时跳过，实际项目中应该实现\n      return true;\n    } catch (error) {\n      console.error('Token验证失败:', error);\n      this.clearUserInfo();\n      return false;\n    }\n  }\n\n  /**\n   * 设置用户信息\n   */\n  setUserInfo(userInfo) {\n    try {\n      Object.assign(this.state, userInfo);\n      this.state.status = USER_STATUS.LOGGED_IN;\n      this.state.loginTime = new Date().toISOString();\n      this.updateLastActiveTime();\n      \n      // 保存到本地存储\n      uni.setStorageSync('userInfo', {\n        userId: this.state.userId,\n        username: this.state.username,\n        nickname: this.state.nickname,\n        avatar: this.state.avatar,\n        phone: this.state.phone,\n        email: this.state.email,\n        isVerified: this.state.isVerified,\n        realName: this.state.realName,\n        status: this.state.status,\n        loginTime: this.state.loginTime,\n        permissions: this.state.permissions,\n        roles: this.state.roles\n      });\n      \n      if (isDebug()) {\n        console.log('用户信息已更新:', this.state);\n      }\n    } catch (error) {\n      console.error('设置用户信息失败:', error);\n    }\n  }\n\n  /**\n   * 更新用户信息\n   */\n  updateUserInfo(updates) {\n    try {\n      Object.assign(this.state, updates);\n      this.updateLastActiveTime();\n      \n      // 更新本地存储\n      const currentUserInfo = uni.getStorageSync('userInfo') || {};\n      const updatedUserInfo = { ...currentUserInfo, ...updates };\n      uni.setStorageSync('userInfo', updatedUserInfo);\n      \n      if (isDebug()) {\n        console.log('用户信息已更新:', updates);\n      }\n    } catch (error) {\n      console.error('更新用户信息失败:', error);\n    }\n  }\n\n  /**\n   * 设置微信用户信息\n   */\n  setWechatUserInfo(wechatInfo) {\n    try {\n      this.state.wechatInfo = wechatInfo;\n      \n      // 同步部分信息到主用户信息\n      if (!this.state.avatar && wechatInfo.avatarUrl) {\n        this.state.avatar = wechatInfo.avatarUrl;\n      }\n      if (!this.state.nickname && wechatInfo.nickName) {\n        this.state.nickname = wechatInfo.nickName;\n      }\n      \n      // 保存到本地存储\n      uni.setStorageSync('wechat_userInfo', wechatInfo);\n      \n      if (isDebug()) {\n        console.log('微信用户信息已设置:', wechatInfo);\n      }\n    } catch (error) {\n      console.error('设置微信用户信息失败:', error);\n    }\n  }\n\n  /**\n   * 清除用户信息\n   */\n  clearUserInfo() {\n    try {\n      // 重置状态\n      Object.assign(this.state, defaultUserData);\n      this.state.isLoading = false;\n      this.state.isInitialized = true;\n      \n      // 清除本地存储\n      uni.removeStorageSync('userInfo');\n      uni.removeStorageSync('wechat_userInfo');\n      uni.removeStorageSync('openid');\n      uni.removeStorageSync('unionid');\n      uni.removeStorageSync('token');\n      \n      if (isDebug()) {\n        console.log('用户信息已清除');\n      }\n    } catch (error) {\n      console.error('清除用户信息失败:', error);\n    }\n  }\n\n  /**\n   * 更新最后活跃时间\n   */\n  updateLastActiveTime() {\n    this.state.lastActiveTime = new Date().toISOString();\n  }\n\n  /**\n   * 用户登出\n   */\n  async logout() {\n    try {\n      await logout();\n      this.clearUserInfo();\n      \n      // 跳转到首页或登录页\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n      \n      uni.showToast({\n        title: '已退出登录',\n        icon: 'success'\n      });\n    } catch (error) {\n      console.error('退出登录失败:', error);\n      uni.showToast({\n        title: '退出登录失败',\n        icon: 'none'\n      });\n    }\n  }\n\n  /**\n   * 检查用户权限\n   */\n  hasPermission(permission) {\n    return this.state.permissions.includes(permission);\n  }\n\n  /**\n   * 检查用户角色\n   */\n  hasRole(role) {\n    return this.state.roles.includes(role);\n  }\n\n  /**\n   * 获取用户状态\n   */\n  getUserState() {\n    return this.state;\n  }\n}\n\n// 创建用户状态管理实例\nconst userStore = new UserStore();\n\n// 计算属性\nexport const userComputed = {\n  // 是否已登录\n  isLoggedIn: computed(() => userStore.state.status !== USER_STATUS.GUEST),\n  \n  // 是否已实名认证\n  isVerified: computed(() => userStore.state.isVerified),\n  \n  // 用户显示名称\n  displayName: computed(() => {\n    return userStore.state.realName || \n           userStore.state.nickname || \n           userStore.state.username || \n           userStore.state.wechatInfo.nickName || \n           '用户';\n  }),\n  \n  // 用户头像\n  displayAvatar: computed(() => {\n    return userStore.state.avatar || \n           userStore.state.wechatInfo.avatarUrl || \n           '/static/tabbar/mine.png';\n  })\n};\n\n// 导出用户状态管理\nexport default userStore;\n"], "names": ["reactive", "checkLogin", "isDebug", "uni", "logout", "computed"], "mappings": ";;;;AAQO,MAAM,cAAc;AAAA,EACzB,OAAO;AAAA;AAAA,EACP,WAAW;AAAA;AAAA,EACX,UAAU;AAAA;AAAA,EACV,UAAU;AAAA;AACZ;AAKA,MAAM,kBAAkB;AAAA;AAAA,EAEtB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA;AAAA,EAGP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,EACX;AAAA;AAAA,EAGD,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA;AAAA,EAGZ,QAAQ,YAAY;AAAA,EACpB,WAAW;AAAA,EACX,gBAAgB;AAAA;AAAA,EAGhB,aAAa,CAAE;AAAA,EACf,OAAO,CAAE;AACX;AAKA,MAAM,UAAU;AAAA,EACd,cAAc;AAEZ,SAAK,QAAQA,uBAAS;AAAA,MACpB,GAAG;AAAA,MACH,WAAW;AAAA,MACX,eAAe;AAAA,IACrB,CAAK;AAGD,SAAK,KAAI;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO;AACX,QAAI;AACF,WAAK,MAAM,YAAY;AAGvB,YAAM,cAAc,MAAMC,iBAAAA;AAE1B,UAAI,YAAY,SAAS;AAEvB,cAAM,KAAK;AAGX,cAAM,KAAK;MACnB,OAAa;AAEL,aAAK,cAAa;AAAA,MACnB;AAED,WAAK,MAAM,gBAAgB;AAE3B,UAAIC,WAAO,QAAA,GAAI;AACbC,sBAAY,MAAA,MAAA,OAAA,6BAAA,cAAc,KAAK,KAAK;AAAA,MACrC;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8BAAA,cAAc,KAAK;AACjC,WAAK,cAAa;AAAA,IACxB,UAAc;AACR,WAAK,MAAM,YAAY;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB;AACtB,QAAI;AACF,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,iBAAiB;AAC3D,YAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,YAAM,UAAUA,cAAAA,MAAI,eAAe,SAAS;AAE5C,UAAI,UAAU;AACZ,eAAO,OAAO,KAAK,OAAO,QAAQ;AAClC,aAAK,MAAM,SAAS,YAAY;AAAA,MACjC;AAED,UAAI,gBAAgB;AAClB,aAAK,MAAM,aAAa;AAExB,YAAI,CAAC,KAAK,MAAM,UAAU,eAAe,WAAW;AAClD,eAAK,MAAM,SAAS,eAAe;AAAA,QACpC;AAED,YAAI,CAAC,KAAK,MAAM,YAAY,eAAe,UAAU;AACnD,eAAK,MAAM,WAAW,eAAe;AAAA,QACtC;AAAA,MACF;AAED,UAAI,QAAQ;AACV,aAAK,MAAM,SAAS;AAAA,MACrB;AAED,UAAI,SAAS;AACX,aAAK,MAAM,UAAU;AAAA,MACtB;AAAA,IAEF,SAAQ,OAAO;AACdA,uEAAc,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,gBAAgB;AACpB,QAAI;AAGF,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8BAAA,cAAc,KAAK;AACjC,WAAK,cAAa;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY,UAAU;AACpB,QAAI;AACF,aAAO,OAAO,KAAK,OAAO,QAAQ;AAClC,WAAK,MAAM,SAAS,YAAY;AAChC,WAAK,MAAM,aAAY,oBAAI,KAAM,GAAC,YAAW;AAC7C,WAAK,qBAAoB;AAGzBA,oBAAG,MAAC,eAAe,YAAY;AAAA,QAC7B,QAAQ,KAAK,MAAM;AAAA,QACnB,UAAU,KAAK,MAAM;AAAA,QACrB,UAAU,KAAK,MAAM;AAAA,QACrB,QAAQ,KAAK,MAAM;AAAA,QACnB,OAAO,KAAK,MAAM;AAAA,QAClB,OAAO,KAAK,MAAM;AAAA,QAClB,YAAY,KAAK,MAAM;AAAA,QACvB,UAAU,KAAK,MAAM;AAAA,QACrB,QAAQ,KAAK,MAAM;AAAA,QACnB,WAAW,KAAK,MAAM;AAAA,QACtB,aAAa,KAAK,MAAM;AAAA,QACxB,OAAO,KAAK,MAAM;AAAA,MAC1B,CAAO;AAED,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAA,MAAA,MAAA,OAAA,8BAAY,YAAY,KAAK,KAAK;AAAA,MACnC;AAAA,IACF,SAAQ,OAAO;AACdA,uEAAc,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,SAAS;AACtB,QAAI;AACF,aAAO,OAAO,KAAK,OAAO,OAAO;AACjC,WAAK,qBAAoB;AAGzB,YAAM,kBAAkBA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAC1D,YAAM,kBAAkB,EAAE,GAAG,iBAAiB,GAAG,QAAO;AACxDA,oBAAAA,MAAI,eAAe,YAAY,eAAe;AAE9C,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAA,MAAA,MAAA,OAAA,8BAAY,YAAY,OAAO;AAAA,MAChC;AAAA,IACF,SAAQ,OAAO;AACdA,uEAAc,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB,YAAY;AAC5B,QAAI;AACF,WAAK,MAAM,aAAa;AAGxB,UAAI,CAAC,KAAK,MAAM,UAAU,WAAW,WAAW;AAC9C,aAAK,MAAM,SAAS,WAAW;AAAA,MAChC;AACD,UAAI,CAAC,KAAK,MAAM,YAAY,WAAW,UAAU;AAC/C,aAAK,MAAM,WAAW,WAAW;AAAA,MAClC;AAGDA,oBAAAA,MAAI,eAAe,mBAAmB,UAAU;AAEhD,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAY,MAAA,MAAA,OAAA,8BAAA,cAAc,UAAU;AAAA,MACrC;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8BAAA,eAAe,KAAK;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACd,QAAI;AAEF,aAAO,OAAO,KAAK,OAAO,eAAe;AACzC,WAAK,MAAM,YAAY;AACvB,WAAK,MAAM,gBAAgB;AAG3BA,0BAAI,kBAAkB,UAAU;AAChCA,0BAAI,kBAAkB,iBAAiB;AACvCA,0BAAI,kBAAkB,QAAQ;AAC9BA,0BAAI,kBAAkB,SAAS;AAC/BA,0BAAI,kBAAkB,OAAO;AAE7B,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAAA,MAAY,MAAA,OAAA,8BAAA,SAAS;AAAA,MACtB;AAAA,IACF,SAAQ,OAAO;AACdA,uEAAc,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,uBAAuB;AACrB,SAAK,MAAM,kBAAiB,oBAAI,KAAM,GAAC,YAAW;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,SAAS;AACb,QAAI;AACF,YAAMC,iBAAM,OAAA;AACZ,WAAK,cAAa;AAGlBD,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACb,CAAO;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8BAAA,WAAW,KAAK;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,YAAY;AACxB,WAAO,KAAK,MAAM,YAAY,SAAS,UAAU;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ,MAAM;AACZ,WAAO,KAAK,MAAM,MAAM,SAAS,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACb,WAAO,KAAK;AAAA,EACb;AACH;AAGK,MAAC,YAAY,IAAI,UAAY;AAGtB,MAAC,eAAe;AAAA;AAAA,EAE1B,YAAYE,cAAQ,SAAC,MAAM,UAAU,MAAM,WAAW,YAAY,KAAK;AAAA;AAAA,EAGvE,YAAYA,cAAAA,SAAS,MAAM,UAAU,MAAM,UAAU;AAAA;AAAA,EAGrD,aAAaA,cAAQ,SAAC,MAAM;AAC1B,WAAO,UAAU,MAAM,YAChB,UAAU,MAAM,YAChB,UAAU,MAAM,YAChB,UAAU,MAAM,WAAW,YAC3B;AAAA,EACX,CAAG;AAAA;AAAA,EAGD,eAAeA,cAAQ,SAAC,MAAM;AAC5B,WAAO,UAAU,MAAM,UAChB,UAAU,MAAM,WAAW,aAC3B;AAAA,EACX,CAAG;AACH;;;"}