<template>
	<view class="solution-confirm-container">
		<!-- 工单基本信息 -->
		<view class="work-order-card">
			<view class="work-order-header">
				<view class="work-order-title">
					<text>工单号: </text>
					<text class="work-order-id">{{workOrderData.id || 'MED20230001'}}</text>
				</view>
				<view class="work-order-status">
					<text class="status-label" :class="{'status-processing': workOrderData.status === '进行中'}">{{workOrderData.status || '进行中'}}</text>
				</view>
			</view>
			<view class="work-order-date">发起日期: {{workOrderData.createDate || '2023-11-01'}}</view>
		</view>
		
		<!-- 进度条 -->
		<view class="progress-bar">
			<view class="progress-steps">
				<view class="progress-step completed">
					<view class="step-circle">1</view>
					<view class="step-line completed"></view>
					<view class="step-label">调解确认</view>
				</view>
				<view class="progress-step completed">
					<view class="step-circle">2</view>
					<view class="step-line completed"></view>
					<view class="step-label">方案确认</view>
				</view>
				<view class="progress-step active">
					<view class="step-circle">3</view>
					<view class="step-line"></view>
					<view class="step-label">协议签署</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">4</view>
					<view class="step-label">完成</view>
				</view>
			</view>
		</view>
		
		<!-- 协议 -->
		<view class="solutions-container">
			<view class="solution-card">
				<view class="solution-bg"></view>
				<view class="agreement-document">
					<view class="agreement-document-icon">
						<i class="fas fa-file-pdf"></i>
					</view>
					<view class="agreement-document-title">《调解协议》</view>
					<text class="agreement-document-tip">请点击下方按钮查看完整PDF协议文件</text>
					<button class="agreement-document-button" @click="handlePreview">
						<i v-if="isLoadingPreview" class="fas fa-spinner fa-spin"></i>
						<i v-else :class="preview.icon"></i>
						{{ isLoadingPreview ? '正在加载PDF' : preview.text }}
					</button>
					<view class="solution-border"></view>
					<view class="confirm-signing">
						<view class="sign">
							<i class="fas fa-pen-nib"></i>
							<view class="sign-title">确认签署</view>
							<text class="sign-tip">{{ isSigned ? '协议签署已完成' : '请勾选下方确认项进行电子签署' }}</text>
						</view>
						<view 
							class="agreement-check"
							:class="{'agreement-check-signed': isSigned}"
							@click="openCanvas">
								<view class="agreement-content">
									<uni-data-checkbox 
										v-model="agreementStatus"
										multiple
										:localdata="checkboxData"
										:disabled="isSigned" />
									<text class="electronic-signature" :class="{'signature-completed': isSigned}">
										{{ isSigned ? '电子签名已生效，协议具有法律效力' : '点击此处进入电子签名界面' }}
									</text>
								</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
		<view class="action-buttons">
			<button 
				class="confirm-button" 
				:class="buttonConfig.class"
				@click="handleConfirm"
				:disabled="isLoading || (!isSigned && !agreementStatus.includes('1'))">
				<!-- 加载状态显示 -->
				<i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
				<i v-else :class="buttonConfig.icon"></i>
				{{ isLoading ? '正在确认...' : buttonConfig.text }}
			</button>
		</view>

		<!-- 引入电子签名 -->
		<canvas-autograph 
			v-model="isCanvas" 
			@complete="complete"
			:showSignatureLine="true"
			signatureLabel="签名："
			:signatureLineY="60"
			signatureLineColor="#2979ff"
		></canvas-autograph>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { api } from '@/utils/api.js';
import CanvasAutograph from '@/components/canvas-autograph/canvas-autograph.vue';

// 工单基本数据
const workOrderData = ref({
	id: 'MED20230001',
	status: '进行中', // 修改为与图片一致的状态
	createDate: '2025-11-01'
});

/**
 * 签名状态管理
 * @type {boolean} 是否已完成签名
 */
const isSigned = ref(false);

/**
 * 加载状态管理
 * @type {boolean} 是否正在处理预览协议
 */
const isLoadingPreview = ref(false);

/**
 * 动态按钮配置计算属性
 * @returns {Object} 按钮配置对象
 */
const preview = computed(() => {
	return {
		icon: 'fas fa-expand',
		text: '全屏查看协议内容',
		class: 'preview-btn'
	};
});
/**
 * 协议同意状态
 * @type {string} 复选框选中值
 */
const agreementStatus = ref([]);
/**
 * 签名数据存储
 * @type {string} base64签名图片数据
 */
const signatureData = ref('');

/**
 * 加载状态管理
 * @type {boolean} 是否正在处理签名
 */
const isLoading = ref(false);

/**
 * 复选框配置数据
 * @type {Array}
 */
const hobbys = ref([
	{
		text: '我已阅读并同意《调解协议》的全部条款',
		value: '1',
		disabled: false
	}
]);

/**
 * 动态复选框配置计算属性
 * 根据签名状态返回不同的复选框配置
 * @returns {Array} 复选框配置数组
 */
const checkboxData = computed(() => {
	if (isSigned.value) {
		return [
			{
				text: '协议签署完成',
				value: 0,
				disabled: true
			}
		];
	}
	return hobbys.value;
});

/**
 * 动态按钮配置计算属性
 * 根据签名状态和复选框状态返回不同的按钮配置
 * @returns {Object} 按钮配置对象
 */
const buttonConfig = computed(() => {
	if (isSigned.value) {
		return {
			icon: 'fas fa-check-circle',
			text: '协议签署完成',
			class: 'success-btn'
		};
	}
	return {
		icon: 'fas fa-file-signature',
		text: '确认协议签署',
		class: !agreementStatus.value.includes('1') ? 'disabled-btn' : 'sign-btn'
	};
});
/**
 * 保存签名状态到本地存储
 */
const saveSignatureState = () => {
	try {
		const stateData = {
			isSigned: isSigned.value,
			agreementStatus: agreementStatus.value,
			signatureData: signatureData.value,
			timestamp: Date.now()
		};
		uni.setStorageSync('agreement_signature_state', JSON.stringify(stateData));
	} catch (error) {
		console.error('保存签名状态失败:', error);
	}
};

/**
 * 从本地存储加载签名状态
 */
const loadSignatureState = () => {
	try {
		const savedState = uni.getStorageSync('agreement_signature_state');
		if (savedState) {
			const stateData = JSON.parse(savedState);
			// 检查数据是否在24小时内有效
			if (Date.now() - stateData.timestamp < 24 * 60 * 60 * 1000) {
				isSigned.value = stateData.isSigned || false;
				agreementStatus.value = stateData.agreementStatus || [];
				signatureData.value = stateData.signatureData || '';
			}
		}
	} catch (error) {
		console.error('加载签名状态失败:', error);
	}
};
/**
 * 全屏查看协议内容按钮点击处理
 * 根据当前状态执行不同的操作逻辑
 */
const handlePreview = async () => {
	try {
		isLoadingPreview.value = true;

		// 模拟加载过程
		await new Promise(resolve => setTimeout(resolve, 1500));
		
		// 跳转到协议预览页面
		uni.navigateTo({
			url: '/pages/protocol_preview/protocol_preview'
		});
	} catch (error) {
		console.error('跳转失败:', error);
		uni.showToast({
			title: '跳转失败，请重试',
			icon: 'error'
		});
	} finally {
		isLoadingPreview.value = false;
	}
};
/**
 * 底部确认按钮点击处理
 * 根据当前状态执行不同的操作逻辑
 */
const handleConfirm = async () => {
	try {
		// 如果已签名，执行确认完成流程并跳转
		if (isSigned.value) {
			isLoading.value = true;
			
			// 模拟确认处理过程
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			uni.showToast({
				title: '协议确认完成',
				icon: 'success',
				duration: 1500
			});
			
			// 延时跳转到下一个页面
			setTimeout(() => {
				// 跳转到工单完成页面或下一个流程页面
				uni.navigateTo({
					url: '/pages/contact_information/contact_information'
				});
			}, 1500);
			
			return;
		}
		
		// 如果已勾选协议但未签名，引导用户进行签名
		if (agreementStatus.value.includes('1')) {
			uni.showModal({
				title: '提示',
				content: '您已同意协议条款，请进行电子签名以完成签署流程',
				confirmText: '去签名',
				cancelText: '稍后',
				success: (res) => {
					if (res.confirm) {
						openCanvas();
					}
				}
			});
		} else {
			// 如果未勾选协议，提示先勾选
			uni.showToast({
				title: '请先勾选同意协议条款',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('处理确认操作失败:', error);
		uni.showToast({
			title: '操作失败，请重试',
			icon: 'error'
		});
	} finally {
		isLoading.value = false;
	}
};

/**
 * 电子签名弹框状态
 * @type {boolean}
 */
const isCanvas = ref(false);

/**
 * 签名完成回调函数
 * 处理签名完成后的状态更新和数据保存
 * @param {string} signatureBase64 - 签名的base64图片数据
 */
const complete = async (signatureBase64) => {
	try {
		isLoading.value = true;
		console.log('签名数据:', signatureBase64);
		
		// 保存签名数据
		signatureData.value = signatureBase64;
		
		// 更新签名状态
		isSigned.value = true;
		agreementStatus.value = ['1'];
		
		// 保存状态到本地存储
		saveSignatureState();
		
		uni.showToast({
			title: '签名保存成功',
			icon: 'success'
		});
	} catch (error) {
		console.error('处理签名失败:', error);
		uni.showToast({
			title: '签名保存失败，请重试',
			icon: 'error'
		});
	} finally {
		isLoading.value = false;
	}
};

/**
 * 打开电子签名界面
 * 检查签名状态，已签名则提示，未签名则打开签名界面
 */
const openCanvas = () => {
	try {
		if (isSigned.value) {
			uni.showToast({
				title: '协议已签署，无需重复操作',
				icon: 'none'
			});
			return;
		}
		
		if (isLoading.value) {
			uni.showToast({
				title: '正在处理中，请稍候',
				icon: 'loading'
			});
			return;
		}
		
		isCanvas.value = true;
	} catch (error) {
		console.error('打开签名界面失败:', error);
		uni.showToast({
			title: '打开签名界面失败',
			icon: 'error'
		});
	}
};

/**
 * 页面加载时执行
 * 恢复之前保存的签名状态
 */
onMounted(() => {
	loadSignatureState();
});
</script>

<style lang="scss" scoped>
:root{
	// 主题色系
	--primary-color:#3b7eeb;
	--primary-dark:#2c62c9; 
	--primary-light: #e6f0ff;
	--success-color: #52c41a;
	--success-dark: #45a049;

	// 文字颜色
	--text-primary: #333;
	--text-secondary: var(--text-secondary);
	--text-disabled: #999;
	
	// 背景色
	--bg-primary: #fff;
	--bg-secondary: #f5f5f5;
	--bg-disabled: #87B0F2;
	
	// 边框和阴影
	--border-radius: 12rpx;
	--border-radius-large: 24rpx;
	--box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	--transition-duration: 0.3s;
}
.solution-confirm-container {
	min-height: 100vh;
	background-color: var(--bg-secondary);
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 工单卡片样式 */
.work-order-card {
	background-color: var(--bg-primary);
	border-radius: var(--border-radius);
	padding: 30rpx;
	box-shadow: var(--box-shadow);
}

.work-order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.work-order-title {
	font-size: 30rpx;
	color: var(--text-primary);
	font-weight: bold;
}

.work-order-status {
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
}

.status-label {
	font-size: 26rpx;
	padding: 8rpx 20rpx;
	border-radius: 30rpx;
	background-color: #f0f0f0;
	color: var(--bg-primary);
}

.status-processing {
	background-color: #1890ff;
	color: var(--bg-primary);
}

.work-order-date {
	font-size: 28rpx;
	color: var(--text-secondary);
}

/* 进度条样式 */
/* .progress-bar {
	background-color: var(--bg-primary);
	border-radius: var(--border-radius);
	padding: 30rpx;
	box-shadow: var(--box-shadow);
} */

.progress-steps {
	display: flex;
	justify-content: space-between;
	position: relative;
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	position: relative;
}

.step-circle {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: #e0e0e0;
	color: var(--bg-primary);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	position: relative;
	z-index: 2;
	transition: all var(--transition-duration) ease;
}

.step-line {
	position: absolute;
	top: 30rpx;
	left: 50%;
	right: -50%;
	height: 4rpx;
	background-color: #e0e0e0;
	z-index: 1;
	transition: all var(--transition-duration) ease;
}

.progress-step:last-child .step-line {
	display: none;
}

.step-label {
	font-size: 24rpx;
	color: var(--text-disabled);
	text-align: center;
	transition: all var(--transition-duration) ease;
}

.progress-step.active .step-circle {
	background-color: var(--primary-color);
}

.progress-step.active .step-label {
	color: var(--primary-color);
	font-weight: bold;
}

.progress-step.completed .step-circle {
	background-color: var(--primary-color);
}

.step-line.completed {
	background-color: var(--primary-color);
}

.progress-step.completed .step-label {
	color: var(--primary-color);
}

/* 方案容器 */
.solutions-container {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 方案卡片样式 */
.solution-card {
	background-color: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(248, 250, 252) 100%);;
	position: relative;
    overflow: hidden;
	border: 2rpx solid var(--primary-color);
	border-radius: var(--border-radius-large);
	transition: all var(--transition-duration) ease;
}

.solution-bg{
	position: absolute;
    top: -40rpx;
    right: -40rpx;
    width: 200rpx;
    height: 200rpx;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    opacity: 0.1;
}
.agreement-document{
	text-align: center;
    padding: 60rpx 40rpx;
}
.agreement-document-icon{
	width: 160rpx;
    height: 160rpx;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
	margin: 0 auto 40rpx;
    box-shadow: 0 16rpx 40rpx rgba(59, 126, 235, 0.3);
    position: relative;
    z-index: 2;
	.fas{
		color: var(--bg-primary);
    	font-size: 64rpx;
	}
}
.agreement-document-title{
	margin: 0 0 16rpx 0;
    color: var(--text-primary);
    font-size: 40rpx;
    font-weight: bold;
}
.agreement-document-tip{
    font-size: 28rpx;
    color: var(--text-secondary);
    line-height: 1.5;
}
.agreement-document-button{
	display: flex;
    align-items: center;
	justify-content: center;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
    box-shadow: rgba(0, 0, 0, 0.1) 0 4rpx 8rpx;
	// min-width: 400rpx;  // 固定最小宽度
    height: 96rpx;
	white-space: nowrap; // 防止文字换行
    line-height: 1.5;
    padding: 24rpx 40rpx;
    border-radius: 16rpx;
    transition: all var(--transition-duration) ease;
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
    margin: 50rpx 110rpx 30rpx 110rpx;
    transform: scale(1);
    will-change: transform; // 启用硬件加速
	background-color: var(--primary-color);
    color: var(--bg-primary);
	.fas{
		margin-right: 20rpx;
    	font-size: 36rpx;
		// width: 36rpx; // 固定图标宽度
        // display: inline-block;
	}
}
.solution-border{
	width: 120rpx;
    height: 6rpx;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 4rpx;
    margin: 40rpx auto;
}
.confirm-signing{
	margin-top: 50rpx;
	.sign{
		margin-bottom: 40rpx;
		.fas{
			font-size: 80rpx;
			color: var(--primary-color);
			margin-bottom: 24rpx;
		}
	}
	.sign-title{
		margin: 0 0 16rpx 0;
		color: var(--text-primary);
		font-size: 36rpx;
		font-weight: bold;
	}
	.sign-tip{
		margin: 0;
		font-size: 28rpx;
		color: var(--text-secondary);
	}
	.agreement-check{
		background-color: var(--bg-primary);
		border: 4rpx solid var(--primary-light);
		border-radius: var(--border-radius-large);
		padding: 36rpx;
		margin: 40rpx 0;
		transition: all var(--transition-duration) ease;
		&:hover {
			box-shadow: 0 4rpx 20rpx rgba(59, 126, 235, 0.1);
		}
	}
	/* 签名完成状态样式 */
	.agreement-check-signed{
		border-color: var(--success-color) !important;
		background-color: rgb(246, 255, 237) !important;
		&:hover {
			box-shadow: 0 4rpx 20rpx rgba(82, 196, 26, 0.1);
		}
	}
	.agreement-content{
		display: flex;
		text-align: left;
    	flex-direction: column;
	}
	.electronic-signature{
		color: var(--text-secondary);
		font-size: 26rpx;
		line-height: 1.4;
		margin-left: 57rpx;
		display: block;
		transition: all var(--transition-duration) ease;
	}
	.signature-completed{
		color: var(--success-color) !important;
	}
}
:deep(.checklist-text){
    font-size: 30rpx !important;
    color: var(--text-primary) !important;
    font-weight: bold !important;
	margin-left: 10rpx !important;
    line-height: 1.5 !important;
	transition: all var(--transition-duration) ease !important;
}
// 签名完成后复选框文字样式
.agreement-check-signed :deep(.checklist-text){
    color: var(--success-color) !important;
}
:deep(.checkbox__inner){
	width: 40rpx !important;
    height: 40rpx !important;
    border: 4rpx solid var(--primary-color) !important;
    border-radius: 8rpx !important;
    background-color: var(--bg-primary);
    z-index: 1 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-right: 15rpx !important;
    transition: all var(--transition-duration) ease !important;
}
:deep(.checkbox__inner-icon){
	height: 18rpx !important;
    width: 10rpx !important;
	border-right-width: 4rpx !important;
	border-bottom-width: 4rpx !important;
	opacity: 1 !important;
}
:deep(.checklist-group .checklist-box.is--default.is-disable .checkbox__inner) {
    background-color: #3b7eeb !important;
    border-color: #3b7eeb!important;
}
// 签名完成后复选框边框样式
.agreement-check-signed :deep(.checkbox__inner){
    border-color: var(--success-color) !important;
}

/* 底部按钮 */
.action-buttons {
	padding: 20rpx 0;
}

.confirm-button {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	border-radius: 16rpx;
	font-size: 32rpx;
    display: flex;
    justify-content: center;
	transition: all var(--transition-duration) ease;
	.fas{
		margin-right: 20rpx;
    	font-size: 36rpx;
		color: var(--bg-primary);
	}
	&:active {
		transform: scale(0.98);
	}
}
.disabled-btn{
	background-color: var(--bg-disabled) !important;
	color: var(--bg-primary) !important;
}

.sign-btn{
	background-color: var(--primary-color) !important;
	color: var(--bg-primary) !important;
	&:hover {
		background-color: var(--primary-dark) !important;
	}
}

.success-btn{
	background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%) !important;
	color: var(--bg-primary) !important;
	&:hover {
		transform: none;
	}
	
	&:active {
		transform: none;
	}
}
</style> 