"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const utils_wechatAuth = require("./wechat-auth.js");
const utils_userStore = require("./user-store.js");
const utils_errorHandler = require("./error-handler.js");
const utils_wechatErrorHandler = require("./wechat-error-handler.js");
const LOGIN_RESULT_TYPE = {
  SUCCESS: "success",
  // 登录成功
  NEED_USER_INFO: "need_user_info",
  // 需要完善用户信息
  FAILED: "failed",
  // 登录失败
  CANCELLED: "cancelled"
  // 用户取消
};
async function performWechatLogin(options = {}) {
  const defaultOptions = {
    baseURL: config_env.getBaseURL(),
    timeout: 15e3,
    // 增加超时时间以支持用户授权流程
    showLoading: true,
    autoNavigate: true,
    enableRetry: true,
    maxRetries: 2,
    showAuthTip: true,
    // 是否显示用户授权说明
    ...options
  };
  const loginOperation = async () => {
    if (defaultOptions.showLoading) {
      common_vendor.index.showLoading({
        title: "登录中...",
        mask: true
      });
    }
    try {
      const loginResult = await utils_wechatAuth.wechatAuth.login({
        baseURL: defaultOptions.baseURL,
        timeout: defaultOptions.timeout,
        showAuthTip: defaultOptions.showAuthTip
      });
      if (loginResult.success) {
        utils_userStore.userStore.setUserInfo(loginResult.userInfo);
        const completeUserData = utils_wechatAuth.wechatAuth.getCompleteUserData();
        const needsUserInfo = !loginResult.wechatUserInfo || utils_wechatAuth.needUserInfo();
        const result2 = {
          type: needsUserInfo ? LOGIN_RESULT_TYPE.NEED_USER_INFO : LOGIN_RESULT_TYPE.SUCCESS,
          success: true,
          userInfo: loginResult.userInfo,
          wechatUserInfo: loginResult.wechatUserInfo,
          // 新增：微信用户信息
          completeUserData,
          needsUserInfo,
          openid: loginResult.openid,
          unionid: loginResult.unionid,
          hasWechatInfo: !!loginResult.wechatUserInfo
          // 新增：是否获取到微信用户信息
        };
        if (defaultOptions.autoNavigate) {
          await handleLoginNavigation(result2);
        }
        return result2;
      } else {
        throw new Error("登录失败");
      }
    } finally {
      if (defaultOptions.showLoading) {
        common_vendor.index.hideLoading();
      }
    }
  };
  try {
    let result2;
    if (defaultOptions.enableRetry) {
      result2 = await utils_wechatErrorHandler.withWechatErrorHandling(loginOperation, {
        maxRetries: defaultOptions.maxRetries,
        showError: false,
        // 我们稍后统一处理错误显示
        onRetry: (attempt, error, delay) => {
          common_vendor.index.showToast({
            title: `登录失败，${Math.round(delay / 1e3)}秒后重试...`,
            icon: "none",
            duration: Math.min(delay, 3e3)
          });
        }
      });
    } else {
      result2 = await loginOperation();
    }
    return result2;
  } catch (error) {
    utils_wechatErrorHandler.showWechatError(error);
    return {
      type: LOGIN_RESULT_TYPE.FAILED,
      success: false,
      error: error.message || "登录失败",
      errorType: utils_errorHandler.ERROR_TYPES.UNKNOWN
    };
  }
  return result;
}
async function handleLoginNavigation(loginResult) {
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : "";
    try {
      await recordLoginOperation({
        action: "login_success",
        loginType: "wechat_miniprogram",
        hasWechatInfo: loginResult.hasWechatInfo,
        needsUserInfo: loginResult.needsUserInfo,
        route: currentRoute
      });
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/login-helper.js:153", "记录登录操作日志失败:", error);
    }
    if (loginResult.type === LOGIN_RESULT_TYPE.NEED_USER_INFO) {
      if (!loginResult.hasWechatInfo) {
        common_vendor.index.showToast({
          title: "建议完善头像昵称信息以获得更好体验",
          icon: "none",
          duration: 3e3
        });
      }
      try {
        common_vendor.index.navigateTo({
          url: "/pages/profile/complete-info"
        });
        return;
      } catch (error) {
        common_vendor.index.__f__("log", "at utils/login-helper.js:176", "没有找到信息完善页面，跳转到个人中心");
      }
    }
    if (currentRoute === "pages/login/login") {
      common_vendor.index.switchTab({
        url: "/pages/mine/mine"
      });
    } else {
      common_vendor.index.showToast({
        title: "登录成功",
        icon: "success",
        duration: 2e3
      });
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/login-helper.js:195", "登录导航处理失败:", error);
    common_vendor.index.switchTab({
      url: "/pages/index/index"
    });
  }
}
async function recordLoginOperation(logData) {
  try {
    const { api } = require("@/utils/api.js");
    await api.operationLog.recordOperation({
      menu_name: "用户登录",
      button_name: logData.action,
      browser_path: `/${logData.route}`,
      operation_details: JSON.stringify({
        loginType: logData.loginType,
        hasWechatInfo: logData.hasWechatInfo,
        needsUserInfo: logData.needsUserInfo,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      })
    });
  } catch (error) {
    common_vendor.index.__f__("warn", "at utils/login-helper.js:224", "记录操作日志失败:", error);
  }
}
async function checkAndHandleLoginStatus(options = {}) {
  const defaultOptions = {
    autoLogin: false,
    autoNavigate: false,
    showLoading: false,
    ...options
  };
  try {
    if (defaultOptions.showLoading) {
      common_vendor.index.showLoading({
        title: "检查登录状态...",
        mask: true
      });
    }
    const result2 = await utils_wechatAuth.autoLoginCheck({
      baseURL: config_env.getBaseURL(),
      autoLogin: defaultOptions.autoLogin
    });
    if (result2.isLogin) {
      if (defaultOptions.autoNavigate) {
        if (result2.needUserInfo) {
          common_vendor.index.navigateTo({
            url: "/pages/user-info/user-info"
          });
        } else {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      }
      return {
        type: result2.needUserInfo ? LOGIN_RESULT_TYPE.NEED_USER_INFO : LOGIN_RESULT_TYPE.SUCCESS,
        success: true,
        isLogin: true,
        needUserInfo: result2.needUserInfo,
        userData: result2.userData
      };
    } else {
      return {
        type: LOGIN_RESULT_TYPE.FAILED,
        success: false,
        isLogin: false,
        needLogin: true,
        reason: result2.reason
      };
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/login-helper.js:286", "检查登录状态失败:", error);
    return {
      type: LOGIN_RESULT_TYPE.FAILED,
      success: false,
      error: error.message
    };
  } finally {
    if (defaultOptions.showLoading) {
      common_vendor.index.hideLoading();
    }
  }
}
function showLoginError(error, options = {}) {
  const defaultOptions = {
    duration: 2e3,
    customMessages: {
      [utils_errorHandler.ERROR_TYPES.NETWORK]: {
        message: "网络连接失败，请检查网络设置"
      },
      [utils_errorHandler.ERROR_TYPES.TIMEOUT]: {
        message: "登录超时，请重试"
      },
      [utils_errorHandler.ERROR_TYPES.AUTHORIZATION]: {
        message: "微信授权失败，请重新登录"
      },
      [utils_errorHandler.ERROR_TYPES.USER_CANCELLED]: {
        message: "用户取消登录"
      }
    },
    ...options
  };
  utils_errorHandler.showError(error, defaultOptions);
}
exports.checkAndHandleLoginStatus = checkAndHandleLoginStatus;
exports.performWechatLogin = performWechatLogin;
exports.showLoginError = showLoginError;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/login-helper.js.map
