"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const server_require = require("../server/require.js");
const utils_api = require("./api.js");
const LOGIN_STATUS = {
  NOT_LOGIN: "not_login",
  // 未登录
  LOGGING: "logging",
  // 登录中
  LOGIN_SUCCESS: "login_success",
  // 登录成功
  LOGIN_FAILED: "login_failed"
  // 登录失败
};
const USER_INFO_STATUS = {
  NOT_AUTHORIZED: "not_authorized",
  // 未授权
  AUTHORIZING: "authorizing",
  // 授权中
  AUTHORIZED: "authorized",
  // 已授权
  AUTHORIZATION_FAILED: "auth_failed"
  // 授权失败
};
class WechatAuth {
  constructor() {
    this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
    this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;
    this.userInfo = null;
    this.wechatUserInfo = null;
    this.sessionKey = null;
    this.openid = null;
    this.unionid = null;
    this.loginRetryCount = 0;
    this.maxRetryCount = 3;
    this.sessionCheckTimer = null;
    this.startSessionCheck();
  }
  /**
   * 启动会话检查定时器 - 每30分钟检查一次登录状态
   */
  startSessionCheck() {
    if (this.sessionCheckTimer) {
      clearInterval(this.sessionCheckTimer);
    }
    this.sessionCheckTimer = setInterval(() => {
      this.checkLoginStatus().catch((error) => {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("warn", "at utils/wechat-auth.js:59", "定时会话检查失败:", error);
        }
      });
    }, 30 * 60 * 1e3);
  }
  /**
   * 停止会话检查定时器
   */
  stopSessionCheck() {
    if (this.sessionCheckTimer) {
      clearInterval(this.sessionCheckTimer);
      this.sessionCheckTimer = null;
    }
  }
  /**
   * 检查微信登录状态 - 增强版本，支持自动续期
   */
  checkLoginStatus() {
    return new Promise((resolve) => {
      common_vendor.wx$1.checkSession({
        success: () => {
          const access_token = common_vendor.index.getStorageSync("access_token");
          const tokenExpireTime = common_vendor.index.getStorageSync("token_expire_time");
          const currentTime = Date.now();
          if (access_token && (!tokenExpireTime || currentTime < tokenExpireTime)) {
            this.loginStatus = LOGIN_STATUS.LOGIN_SUCCESS;
            resolve({
              isLogin: true,
              needReLogin: false,
              tokenValid: true
            });
          } else {
            this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
            resolve({
              isLogin: false,
              needReLogin: true,
              tokenValid: false,
              reason: "token_expired"
            });
          }
        },
        fail: (error) => {
          this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
          if (config_env.isDebug()) {
            common_vendor.index.__f__("log", "at utils/wechat-auth.js:109", "微信会话已失效:", error);
          }
          resolve({
            isLogin: false,
            needReLogin: true,
            tokenValid: false,
            reason: "session_expired"
          });
        }
      });
    });
  }
  /**
   * 微信登录 - 增强版本，支持完整的用户授权流程
   * @param {Object} options 登录选项
   * @returns {Promise}
   */
  login(options = {}) {
    return new Promise((resolve, reject) => {
      if (this.loginStatus === LOGIN_STATUS.LOGGING) {
        reject(new Error("正在登录中，请勿重复操作"));
        return;
      }
      this.loginRetryCount = 0;
      this._performLogin(options, resolve, reject);
    });
  }
  /**
   * 执行登录操作（内部方法，支持重试和完整流程）
   */
  _performLogin(options, resolve, reject) {
    this.loginStatus = LOGIN_STATUS.LOGGING;
    const timeout = options.timeout || 15e3;
    const timeoutTimer = setTimeout(() => {
      this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;
      reject(new Error("登录超时，请检查网络连接"));
    }, timeout);
    this._getUserProfileWithFallback(options).then((userProfileResult) => {
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/wechat-auth.js:157", "用户信息获取完成:", userProfileResult);
      }
      return this._getWechatLoginCode();
    }).then((code) => {
      clearTimeout(timeoutTimer);
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/wechat-auth.js:167", "微信登录获取code成功:", code);
      }
      return this.exchangeSessionKey(code, options);
    }).then(resolve).catch((error) => {
      clearTimeout(timeoutTimer);
      if (this.loginRetryCount < this.maxRetryCount && (error.message.includes("网络") || error.message.includes("超时"))) {
        this.loginRetryCount++;
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:182", `登录失败，正在重试 (${this.loginRetryCount}/${this.maxRetryCount}):`, error.message);
        }
        setTimeout(() => {
          this._performLogin(options, resolve, reject);
        }, 1e3 * this.loginRetryCount);
      } else {
        this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;
        reject(error);
      }
    });
  }
  /**
   * 获取用户信息授权（支持降级处理）
   * @param {Object} options 选项
   * @returns {Promise}
   */
  _getUserProfileWithFallback(options = {}) {
    return new Promise((resolve) => {
      if (typeof common_vendor.wx$1.getUserProfile === "function") {
        if (options.showAuthTip !== false) {
          common_vendor.index.showModal({
            title: "用户信息授权",
            content: "为了提供更好的服务体验，需要获取您的微信头像和昵称信息，该信息仅用于个人账户展示",
            confirmText: "同意授权",
            cancelText: "暂不授权",
            success: (modalRes) => {
              if (modalRes.confirm) {
                this._callGetUserProfile(resolve);
              } else {
                if (config_env.isDebug()) {
                  common_vendor.index.__f__("log", "at utils/wechat-auth.js:216", "用户拒绝授权，继续登录流程");
                }
                this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
                resolve({
                  success: false,
                  reason: "user_denied",
                  message: "用户拒绝授权，已跳过用户信息获取",
                  shouldContinueLogin: true
                  // 标记应该继续登录流程
                });
              }
            },
            fail: () => {
              if (config_env.isDebug()) {
                common_vendor.index.__f__("log", "at utils/wechat-auth.js:230", "授权弹窗显示失败，继续登录流程");
              }
              this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
              resolve({
                success: false,
                reason: "modal_failed",
                message: "授权弹窗显示失败，已跳过用户信息获取",
                shouldContinueLogin: true
              });
            }
          });
        } else {
          this._callGetUserProfile(resolve);
        }
      } else {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:248", "getUserProfile API不可用，跳过用户信息获取");
        }
        this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;
        resolve({
          success: false,
          reason: "api_unavailable",
          message: "getUserProfile API不可用",
          shouldContinueLogin: true
        });
      }
    });
  }
  /**
   * 调用getUserProfile API
   * @param {Function} resolve Promise resolve函数
   */
  _callGetUserProfile(resolve) {
    this.userInfoStatus = USER_INFO_STATUS.AUTHORIZING;
    common_vendor.wx$1.getUserProfile({
      desc: "用于完善会员资料",
      // 必填，说明获取用户信息的用途
      success: (res) => {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:272", "获取用户信息成功:", res.userInfo);
        }
        this.wechatUserInfo = res.userInfo;
        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;
        common_vendor.index.setStorageSync("wechat_userInfo", res.userInfo);
        resolve({
          success: true,
          userInfo: res.userInfo,
          message: "用户信息获取成功"
        });
      },
      fail: (error) => {
        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
        if (config_env.isDebug()) {
          common_vendor.index.__f__("error", "at utils/wechat-auth.js:291", "getUserProfile失败:", error);
        }
        resolve({
          success: false,
          reason: "get_profile_failed",
          error: error.errMsg,
          message: "获取用户信息失败，但继续登录流程",
          shouldContinueLogin: true
          // 继续登录流程
        });
      }
    });
  }
  /**
   * 获取微信登录code
   * @returns {Promise<string>}
   */
  _getWechatLoginCode() {
    return new Promise((resolve, reject) => {
      common_vendor.wx$1.login({
        success: (loginRes) => {
          if (loginRes.code) {
            resolve(loginRes.code);
          } else {
            reject(new Error("微信登录失败：" + loginRes.errMsg));
          }
        },
        fail: (error) => {
          reject(new Error("微信登录失败：" + error.errMsg));
        }
      });
    });
  }
  /**
   * 向后端换取session_key - 增强版本，支持用户信息传递
   * @param {string} code 微信登录code
   * @param {Object} options 选项
   */
  exchangeSessionKey(code, options = {}) {
    return new Promise((resolve, reject) => {
      options.requestTimeout || 8e3;
      let requestData = {
        js_code: code
      };
      if (this.wechatUserInfo) {
        requestData.nickname = this.wechatUserInfo.nickName;
        requestData.avatar_url = this.wechatUserInfo.avatarUrl;
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:346", requestData, "包含用户信息的登录请求:", {
            js_code: code,
            hasUserInfo: true,
            userInfo: this.wechatUserInfo
          });
        }
      } else {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:354", "仅包含code的登录请求:", { js_code: code });
        }
      }
      utils_api.api.wechat.login(requestData).then((responseData) => {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:362", "后端登录响应:", responseData);
        }
        try {
          const { access_token, token_type, userInfo, openid, unionid, sessionKey, expiresIn, refresh_token } = responseData;
          if (access_token) {
            server_require.authUtils.setTokenInfo({
              access_token,
              token_type,
              expires_in: expiresIn
            });
          }
          if (refresh_token) {
            server_require.authUtils.setRefreshToken(refresh_token);
          }
          this.saveLoginInfo({
            access_token,
            token_type,
            userInfo,
            openid,
            unionid,
            sessionKey,
            expiresIn
          });
          this.loginStatus = LOGIN_STATUS.LOGIN_SUCCESS;
          if (this.wechatUserInfo) {
            common_vendor.index.showToast({
              title: "登录成功",
              icon: "success",
              duration: 2e3
            });
          } else {
            common_vendor.index.showToast({
              title: "登录成功，建议完善个人信息",
              icon: "none",
              duration: 3e3
            });
          }
          resolve({
            success: true,
            access_token,
            token_type,
            userInfo,
            openid,
            unionid,
            expiresIn,
            refresh_token,
            wechatUserInfo: this.wechatUserInfo
            // 可能为null，但这是正常的
          });
        } catch (error) {
          if (config_env.isDebug()) {
            common_vendor.index.__f__("error", "at utils/wechat-auth.js:426", "登录数据解析错误:", error);
          }
          this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;
          reject(new Error("登录数据处理失败"));
        }
      }).catch((error) => {
        this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;
        if (config_env.isDebug()) {
          common_vendor.index.__f__("error", "at utils/wechat-auth.js:436", "微信登录API调用失败:", error);
        }
        reject(error);
      });
    });
  }
  /**
   * 获取用户信息（保留原有方法，增加更好的错误提示）
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      if (typeof common_vendor.wx$1.getUserProfile === "function") {
        common_vendor.wx$1.getUserProfile({
          desc: "用于完善用户资料",
          success: (res) => {
            if (config_env.isDebug()) {
              common_vendor.index.__f__("log", "at utils/wechat-auth.js:456", "获取用户信息成功:", res.userInfo);
            }
            this.wechatUserInfo = res.userInfo;
            this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;
            common_vendor.index.setStorageSync("wechat_userInfo", res.userInfo);
            resolve(res.userInfo);
          },
          fail: (error) => {
            this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
            let errorMessage = "获取用户信息失败";
            if (error.errMsg && error.errMsg.includes("deny")) {
              errorMessage = "用户拒绝授权获取信息";
            } else if (error.errMsg && error.errMsg.includes("auth")) {
              errorMessage = "用户信息授权失败";
            }
            if (config_env.isDebug()) {
              common_vendor.index.__f__("error", "at utils/wechat-auth.js:478", "getUserProfile失败:", error);
            }
            reject(new Error(errorMessage));
          }
        });
      } else {
        reject(new Error("当前微信版本不支持getUserProfile，请升级微信版本或使用其他登录方式"));
      }
    });
  }
  /**
   * 处理头像选择 - 新的头像昵称填写能力
   * @param {Object} event 头像选择事件对象
   * @returns {Promise}
   */
  handleChooseAvatar(event) {
    return new Promise((resolve, reject) => {
      try {
        const { avatarUrl } = event.detail;
        if (!avatarUrl) {
          reject(new Error("未获取到头像信息"));
          return;
        }
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:505", "用户选择头像:", avatarUrl);
        }
        const currentUserInfo = this.wechatUserInfo || {};
        const updatedUserInfo = {
          ...currentUserInfo,
          avatarUrl,
          updateTime: Date.now()
        };
        this.wechatUserInfo = updatedUserInfo;
        common_vendor.index.setStorageSync("wechat_userInfo", updatedUserInfo);
        resolve({
          success: true,
          avatarUrl,
          userInfo: updatedUserInfo
        });
      } catch (error) {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("error", "at utils/wechat-auth.js:526", "处理头像选择失败:", error);
        }
        reject(new Error("处理头像选择失败：" + error.message));
      }
    });
  }
  /**
   * 处理昵称输入 - 新的头像昵称填写能力
   * @param {string} nickname 用户输入的昵称
   * @returns {Promise}
   */
  handleNicknameInput(nickname) {
    return new Promise((resolve, reject) => {
      try {
        if (!nickname || nickname.trim().length === 0) {
          reject(new Error("昵称不能为空"));
          return;
        }
        if (nickname.length > 20) {
          reject(new Error("昵称长度不能超过20个字符"));
          return;
        }
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at utils/wechat-auth.js:553", "用户输入昵称:", nickname);
        }
        const currentUserInfo = this.wechatUserInfo || {};
        const updatedUserInfo = {
          ...currentUserInfo,
          nickName: nickname.trim(),
          updateTime: Date.now()
        };
        this.wechatUserInfo = updatedUserInfo;
        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;
        common_vendor.index.setStorageSync("wechat_userInfo", updatedUserInfo);
        resolve({
          success: true,
          nickName: nickname.trim(),
          userInfo: updatedUserInfo
        });
      } catch (error) {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("error", "at utils/wechat-auth.js:575", "处理昵称输入失败:", error);
        }
        reject(new Error("处理昵称输入失败：" + error.message));
      }
    });
  }
  /**
   * 保存登录信息到本地存储 - 增强版本，支持过期时间管理
   */
  saveLoginInfo({ access_token, token_type, userInfo, openid, unionid, sessionKey, expiresIn }) {
    try {
      const currentTime = Date.now();
      if (access_token) {
        common_vendor.index.setStorageSync("access_token", access_token);
        const tokenType = token_type || "Bearer";
        common_vendor.index.setStorageSync("token_type", tokenType);
        const expireTime = expiresIn ? currentTime + expiresIn * 1e3 : currentTime + 7 * 24 * 60 * 60 * 1e3;
        common_vendor.index.setStorageSync("token_expire_time", expireTime);
      }
      if (userInfo) {
        common_vendor.index.setStorageSync("userInfo", userInfo);
        this.userInfo = userInfo;
      }
      if (openid) {
        common_vendor.index.setStorageSync("openid", openid);
        this.openid = openid;
      }
      if (unionid) {
        common_vendor.index.setStorageSync("unionid", unionid);
        this.unionid = unionid;
      }
      if (sessionKey) {
        this.sessionKey = sessionKey;
      }
      common_vendor.index.setStorageSync("login_time", currentTime);
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/wechat-auth.js:627", "登录信息已保存到本地存储", {
          hasToken: !!access_token,
          hasUserInfo: !!userInfo,
          hasOpenid: !!openid,
          hasUnionid: !!unionid,
          expiresIn
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/wechat-auth.js:636", "保存登录信息失败:", error);
      throw new Error("保存登录信息失败");
    }
  }
  /**
   * 清除登录信息 - 增强版本
   */
  clearLoginInfo() {
    try {
      this.stopSessionCheck();
      const keysToRemove = [
        "access_token",
        "token_type",
        "token_expire_time",
        "userInfo",
        "openid",
        "unionid",
        "wechat_userInfo",
        "login_time"
      ];
      keysToRemove.forEach((key) => {
        try {
          common_vendor.index.removeStorageSync(key);
        } catch (e) {
          if (config_env.isDebug()) {
            common_vendor.index.__f__("warn", "at utils/wechat-auth.js:666", `清除存储项 ${key} 失败:`, e);
          }
        }
      });
      this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
      this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;
      this.userInfo = null;
      this.wechatUserInfo = null;
      this.sessionKey = null;
      this.openid = null;
      this.unionid = null;
      this.loginRetryCount = 0;
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/wechat-auth.js:682", "登录信息已完全清除");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/wechat-auth.js:685", "清除登录信息失败:", error);
      throw new Error("清除登录信息失败");
    }
  }
  /**
   * 获取当前登录状态
   */
  getLoginStatus() {
    return {
      loginStatus: this.loginStatus,
      userInfoStatus: this.userInfoStatus
    };
  }
  /**
   * 获取存储的用户信息 - 增强版本，包含完整性检查
   */
  getStoredUserInfo() {
    try {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      const wechatUserInfo = common_vendor.index.getStorageSync("wechat_userInfo");
      const loginTime = common_vendor.index.getStorageSync("login_time");
      const tokenExpireTime = common_vendor.index.getStorageSync("token_expire_time");
      return {
        userInfo,
        wechatUserInfo,
        loginTime,
        tokenExpireTime,
        isTokenValid: tokenExpireTime ? Date.now() < tokenExpireTime : false,
        hasCompleteUserInfo: !!(wechatUserInfo && wechatUserInfo.nickName && wechatUserInfo.avatarUrl)
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/wechat-auth.js:719", "获取存储的用户信息失败:", error);
      return {
        userInfo: null,
        wechatUserInfo: null,
        loginTime: null,
        tokenExpireTime: null,
        isTokenValid: false,
        hasCompleteUserInfo: false
      };
    }
  }
  /**
   * 获取完整的用户数据对象
   */
  getCompleteUserData() {
    const storedInfo = this.getStoredUserInfo();
    const openid = this.getOpenid();
    const unionid = this.getUnionid();
    return {
      // 基础登录信息
      openid,
      unionid,
      loginTime: storedInfo.loginTime,
      tokenExpireTime: storedInfo.tokenExpireTime,
      isTokenValid: storedInfo.isTokenValid,
      // 后端返回的用户信息
      userInfo: storedInfo.userInfo,
      // 微信用户信息（通过头像昵称填写能力获取）
      wechatUserInfo: storedInfo.wechatUserInfo,
      // 状态信息
      loginStatus: this.loginStatus,
      userInfoStatus: this.userInfoStatus,
      hasCompleteUserInfo: storedInfo.hasCompleteUserInfo
    };
  }
  /**
   * 获取openid
   */
  getOpenid() {
    return this.openid || common_vendor.index.getStorageSync("openid");
  }
  /**
   * 获取unionid
   */
  getUnionid() {
    return this.unionid || common_vendor.index.getStorageSync("unionid");
  }
  /**
   * 检查是否需要获取用户信息
   */
  needUserInfo() {
    const { hasCompleteUserInfo } = this.getStoredUserInfo();
    return !hasCompleteUserInfo;
  }
  /**
   * 销毁实例（清理资源）
   */
  destroy() {
    this.stopSessionCheck();
    this.clearLoginInfo();
  }
}
const wechatAuth = new WechatAuth();
function checkLogin() {
  return wechatAuth.checkLoginStatus();
}
function logout() {
  wechatAuth.clearLoginInfo();
  return Promise.resolve({ success: true });
}
function needUserInfo() {
  return wechatAuth.needUserInfo();
}
function autoLoginCheck(options = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const loginStatus = await wechatAuth.checkLoginStatus();
      if (loginStatus.isLogin) {
        resolve({
          isLogin: true,
          userData: wechatAuth.getCompleteUserData(),
          needUserInfo: wechatAuth.needUserInfo()
        });
      } else if (options.autoLogin !== false) {
        try {
          const loginResult = await wechatAuth.login(options);
          resolve({
            isLogin: true,
            userData: wechatAuth.getCompleteUserData(),
            needUserInfo: wechatAuth.needUserInfo(),
            autoLoginSuccess: true
          });
        } catch (loginError) {
          resolve({
            isLogin: false,
            needLogin: true,
            autoLoginFailed: true,
            error: loginError.message
          });
        }
      } else {
        resolve({
          isLogin: false,
          needLogin: true,
          reason: loginStatus.reason
        });
      }
    } catch (error) {
      reject(error);
    }
  });
}
exports.autoLoginCheck = autoLoginCheck;
exports.checkLogin = checkLogin;
exports.logout = logout;
exports.needUserInfo = needUserInfo;
exports.wechatAuth = wechatAuth;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/wechat-auth.js.map
