{"version": 3, "file": "api.js", "sources": ["server/api.js"], "sourcesContent": ["// API路径定义模块\r\n// 按功能模块组织所有API端点路径\r\n\r\n/**\r\n * 用户相关API路径\r\n */\r\nexport const USER_API = {\r\n  // 基础用户操作\r\n  INFO: '/user/user_info/',\r\n  LOGIN: '/user/login', \r\n  UPDATE: '/user/update',\r\n  BIND_PHONE: '/user/bind-phone',\r\n  SEND_CODE: '/user/send-code',\r\n  PHONE_LOGIN: '/user/phone-login',\r\n  \r\n  // 用户操作日志\r\n  OPERATION_LOG: '/user/operation_log/'\r\n};\r\n\r\n/**\r\n * 微信认证相关API路径\r\n */\r\nexport const WECHAT_API = {\r\n  LOGIN: '/user/wechat/login/',\r\n  REFRESH: '/user/wechat/refresh/',\r\n  BIND: '/wechat/bind/',\r\n  UNBIND: '/wechat/unbind/',\r\n  USER_INFO: '/wechat/userinfo/'\r\n};\r\n\r\n/**\r\n * 首页相关API路径\r\n */\r\nexport const HOME_API = {\r\n  GRID_DATA: '/home/<USER>'\r\n};\r\n\r\n/**\r\n * 调解查询相关API路径\r\n */\r\nexport const MEDIATION_QUERY_API = {\r\n  LIST: '/mediation-query/list',\r\n  DETAIL: '/mediation-query/detail',\r\n  CASE_BY_NUMBER: '/mediation_management/mediation_case/',\r\n  CASE_COUNT_BY_IDENTITY: '/mediation_management/mediation_case/count'\r\n};\r\n\r\n/**\r\n * 工单相关API路径\r\n */\r\nexport const WORK_ORDER_API = {\r\n  DETAIL: '/work-order/detail',\r\n  ACCEPT: '/work-order/accept',\r\n  REJECT: '/work-order/reject'\r\n};\r\n\r\n/**\r\n * 调解方案相关API路径\r\n */\r\nexport const SOLUTION_API = {\r\n  DETAIL: '/solution/detail',\r\n  CONFIRM: '/solution/confirm',\r\n  ADJUST: '/solution/adjust'\r\n};\r\n\r\n/**\r\n * 债权确认相关API路径\r\n */\r\nexport const DEBT_CONFIRM_API = {\r\n  LIST: '/debt-confirm/list',\r\n  DETAIL: '/debt-confirm/detail',\r\n  SUBMIT: '/debt-confirm/submit'\r\n};\r\n\r\n/**\r\n * 调解投诉相关API路径\r\n */\r\nexport const MEDIATION_COMPLAINT_API = {\r\n  LIST: '/mediation-complaint/list',\r\n  DETAIL: '/mediation-complaint/detail',\r\n  SUBMIT: '/mediation-complaint/submit'\r\n};\r\n\r\n/**\r\n * 案例展示相关API路径\r\n */\r\nexport const REAL_CASE_API = {\r\n  LIST: '/case_display/case_display/',\r\n  DETAIL: '/real-case/detail'\r\n};\r\n\r\n/**\r\n * 意见反馈相关API路径\r\n */\r\nexport const FEEDBACK_API = {\r\n  SUBMIT: '/feedback/submit',\r\n  HISTORY: '/feedback/history'\r\n};\r\n\r\n/**\r\n * 所有API路径的统一导出\r\n */\r\nexport const API_PATHS = {\r\n  USER: USER_API,\r\n  WECHAT: WECHAT_API,\r\n  HOME: HOME_API,\r\n  MEDIATION_QUERY: MEDIATION_QUERY_API,\r\n  WORK_ORDER: WORK_ORDER_API,\r\n  SOLUTION: SOLUTION_API,\r\n  DEBT_CONFIRM: DEBT_CONFIRM_API,\r\n  MEDIATION_COMPLAINT: MEDIATION_COMPLAINT_API,\r\n  REAL_CASE: REAL_CASE_API,\r\n  FEEDBACK: FEEDBACK_API\r\n};\r\n\r\n/**\r\n * 根据路径模板和参数生成完整URL\r\n * @param {string} pathTemplate - 路径模板（如 '/user/detail/{id}'）\r\n * @param {object} params - 参数对象\r\n * @returns {string} 完整的URL路径\r\n */\r\nexport const buildApiPath = (pathTemplate, params = {}) => {\r\n  let path = pathTemplate;\r\n  \r\n  // 替换路径中的参数占位符\r\n  Object.keys(params).forEach(key => {\r\n    path = path.replace(`{${key}}`, params[key]);\r\n  });\r\n  \r\n  return path;\r\n};\r\n\r\n/**\r\n * 获取带参数的API路径辅助函数\r\n */\r\nexport const getApiPath = {\r\n  // 获取用户详情路径\r\n  userDetail: (id) => `${USER_API.DETAIL}/${id}`,\r\n  \r\n  // 获取调解查询详情路径\r\n  mediationQueryDetail: (id) => `${MEDIATION_QUERY_API.DETAIL}/${id}`,\r\n  \r\n  // 获取工单详情路径\r\n  workOrderDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}`,\r\n  \r\n  // 获取工单接受路径\r\n  workOrderAccept: (id) => `${WORK_ORDER_API.ACCEPT}/${id}`,\r\n  \r\n  // 获取工单拒绝路径\r\n  workOrderReject: (id) => `${WORK_ORDER_API.REJECT}/${id}`,\r\n  \r\n  // 获取调解方案详情路径\r\n  solutionDetail: (orderId) => `${SOLUTION_API.DETAIL}/${orderId}`,\r\n  \r\n  // 获取调解方案确认路径\r\n  solutionConfirm: (orderId) => `${SOLUTION_API.CONFIRM}/${orderId}`,\r\n  \r\n  // 获取调解方案调整路径\r\n  solutionAdjust: (orderId) => `${SOLUTION_API.ADJUST}/${orderId}`,\r\n  \r\n  // 获取债权确认详情路径\r\n  debtConfirmDetail: (id) => `${DEBT_CONFIRM_API.DETAIL}/${id}`,\r\n  \r\n  // 获取调解投诉详情路径\r\n  mediationComplaintDetail: (id) => `${MEDIATION_COMPLAINT_API.DETAIL}/${id}`,\r\n  \r\n  // 获取案例详情路径\r\n  realCaseDetail: (id) => `${REAL_CASE_API.DETAIL}/${id}`\r\n};\r\n"], "names": [], "mappings": ";AAMY,MAAC,WAAW;AAAA;AAAA,EAEtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA;AAAA,EAGb,eAAe;AACjB;AAKY,MAAC,aAAa;AAAA,EACxB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,WAAW;AACb;AAKY,MAAC,WAAW;AAAA,EACtB,WAAW;AACb;AAKY,MAAC,sBAAsB;AAAA,EACjC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,wBAAwB;AAC1B;AAKO,MAAM,iBAAiB;AAAA,EAC5B,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AAKO,MAAM,eAAe;AAAA,EAC1B,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AACV;AAKY,MAAC,mBAAmB;AAAA,EAC9B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAKY,MAAC,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAKY,MAAC,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,QAAQ;AACV;AAKY,MAAC,eAAe;AAAA,EAC1B,QAAQ;AAAA,EACR,SAAS;AACX;AAsCY,MAAC,aAAa;AAAA;AAAA,EAExB,YAAY,CAAC,OAAO,GAAG,SAAS,MAAM,IAAI,EAAE;AAAA;AAAA,EAG5C,sBAAsB,CAAC,OAAO,GAAG,oBAAoB,MAAM,IAAI,EAAE;AAAA;AAAA,EAGjE,iBAAiB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAGvD,iBAAiB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAGvD,iBAAiB,CAAC,OAAO,GAAG,eAAe,MAAM,IAAI,EAAE;AAAA;AAAA,EAGvD,gBAAgB,CAAC,YAAY,GAAG,aAAa,MAAM,IAAI,OAAO;AAAA;AAAA,EAG9D,iBAAiB,CAAC,YAAY,GAAG,aAAa,OAAO,IAAI,OAAO;AAAA;AAAA,EAGhE,gBAAgB,CAAC,YAAY,GAAG,aAAa,MAAM,IAAI,OAAO;AAAA;AAAA,EAG9D,mBAAmB,CAAC,OAAO,GAAG,iBAAiB,MAAM,IAAI,EAAE;AAAA;AAAA,EAG3D,0BAA0B,CAAC,OAAO,GAAG,wBAAwB,MAAM,IAAI,EAAE;AAAA;AAAA,EAGzE,gBAAgB,CAAC,OAAO,GAAG,cAAc,MAAM,IAAI,EAAE;AACvD;;;;;;;;;;"}