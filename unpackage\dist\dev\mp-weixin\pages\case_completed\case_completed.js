"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "case_completed",
  setup(__props) {
    const orderId = common_vendor.ref("");
    const workOrderData = common_vendor.ref({
      id: "MED20230001",
      status: "已完成",
      createDate: "2023-11-01",
      creditor: "某银行信用卡中心",
      amount: "50,000.00",
      reduction_amount: "10,000.00",
      monthlyRepayment: "2,083.33",
      notarizationStatus: "未公证",
      paymentMemo: "MED20230003_张先生_123456_华泰民商事调解中心"
    });
    const isCopied = common_vendor.ref(false);
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = (_a = currentPage.$page) == null ? void 0 : _a.options;
      if (options && options.id) {
        orderId.value = options.id;
        common_vendor.index.__f__("log", "at pages/case_completed/case_completed.vue:294", "接收到工单ID:", orderId.value);
        fetchWorkOrderDetail(orderId.value);
      } else {
        fetchWorkOrderDetail();
      }
    });
    const fetchWorkOrderDetail = (id) => {
      if (id) {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        utils_api.api.workOrder.getDetail(id).then((res) => {
          common_vendor.index.hideLoading();
          if (res.code === 0) {
            workOrderData.value = res.data;
          } else {
            common_vendor.index.showToast({
              title: res.message || "获取调解确认失败",
              icon: "none"
            });
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/case_completed/case_completed.vue:325", "获取调解确认失败", err);
          common_vendor.index.showToast({
            title: "获取调解确认失败",
            icon: "none"
          });
        });
      } else {
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages/case_completed/case_completed.vue:334", "调解确认数据已加载（模拟）");
        }, 500);
      }
    };
    const handleViewProtocol = () => {
      common_vendor.index.navigateTo({
        url: "/pages/protocol_preview/protocol_preview"
      });
    };
    const handleNotarization = () => {
      common_vendor.index.navigateTo({
        url: "/pages/agreement_notarization/agreement_notarization"
      });
    };
    const copyMemo = () => {
      common_vendor.index.setClipboardData({
        data: workOrderData.value.paymentMemo
      });
      common_vendor.index.showToast({
        title: "还款备注信息已复制",
        icon: "success"
      });
      isCopied.value = true;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(workOrderData.value.id || "MED20230001"),
        b: common_vendor.t(workOrderData.value.status || "已完成"),
        c: workOrderData.value.status === "已完成" ? 1 : "",
        d: common_vendor.t(workOrderData.value.createDate || "2023-11-01"),
        e: workOrderData.value.notarizationStatus === "未公证"
      }, workOrderData.value.notarizationStatus === "未公证" ? {
        f: common_vendor.o(handleNotarization)
      } : {}, {
        g: workOrderData.value.notarizationStatus === "已公证"
      }, workOrderData.value.notarizationStatus === "已公证" ? {} : {}, {
        h: common_vendor.t(workOrderData.value.creditor || "某银行信用卡中心"),
        i: common_vendor.t(workOrderData.value.debtType || "信用卡欠款"),
        j: common_vendor.t(workOrderData.value.amount || "50,000.00"),
        k: common_vendor.t(workOrderData.value.debtDate || "2022-05-01"),
        l: common_vendor.t(workOrderData.value.creditor || "免除全部逾期利息，本金分24期等额还款"),
        m: common_vendor.t(workOrderData.value.amount || "50,000.00"),
        n: common_vendor.t(workOrderData.value.monthlyRepayment || "2,083.33"),
        o: common_vendor.t(workOrderData.value.reduction_amount || "10,000.00"),
        p: common_vendor.t(workOrderData.value.paymentMemo),
        q: !isCopied.value
      }, !isCopied.value ? {
        r: common_vendor.o(copyMemo)
      } : {}, {
        s: common_vendor.o(handleViewProtocol)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf6b09e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/case_completed/case_completed.js.map
