/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.protocol-preview-container.data-v-3f6102ab {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
}
.preview-options.data-v-3f6102ab {
  display: flex;
  gap: 30rpx;
  margin-bottom: 40rpx;
}
.option-btn.data-v-3f6102ab {
  flex: 1;
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.option-btn.data-v-3f6102ab:active {
  transform: scale(0.98);
}
.option-btn .fas.data-v-3f6102ab {
  font-size: 48rpx;
  color: #3b7eeb;
  margin-bottom: 16rpx;
}
.option-btn text.data-v-3f6102ab {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.option-btn .option-desc.data-v-3f6102ab {
  font-size: 24rpx !important;
  color: #666 !important;
  font-weight: normal !important;
  margin-bottom: 0 !important;
}

/* 长图预览样式 */
.long-image-preview.data-v-3f6102ab {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  z-index: 9999;
}
.preview-header.data-v-3f6102ab {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 10000;
}

/* .header-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.back-btn {
	background: transparent;
	border: none;
	color: #333;
	font-size: 32rpx;
	padding: 8rpx;
}

.doc-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
} */
.progress-text.data-v-3f6102ab {
  font-size: 26rpx;
  color: #666;
}
.long-scroll-container.data-v-3f6102ab {
  width: 100%;
  height: 100vh;
  padding-top: 88rpx;
}
.content-container.data-v-3f6102ab {
  min-height: calc(100vh - 88rpx);
}
.loading-container.data-v-3f6102ab {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}
.loading-container .loading-icon.data-v-3f6102ab {
  margin-bottom: 30rpx;
}
.loading-container .loading-icon .fas.data-v-3f6102ab {
  font-size: 48rpx;
  color: #3b7eeb;
}
.loading-container .loading-text.data-v-3f6102ab {
  font-size: 28rpx;
  color: #666;
}
.images-container.data-v-3f6102ab {
  background-color: #fff;
}
.protocol-page.data-v-3f6102ab {
  width: 100%;
  display: block;
  border-bottom: 2rpx solid #f0f0f0;
}
.protocol-page.first-page.data-v-3f6102ab {
  border-top: none;
}
.bottom-tip.data-v-3f6102ab {
  padding: 60rpx 40rpx;
  text-align: center;
  background-color: #f9f9f9;
}
.bottom-tip .tip-line.data-v-3f6102ab {
  width: 100rpx;
  height: 4rpx;
  background-color: #e0e0e0;
  margin: 0 auto 30rpx;
  border-radius: 2rpx;
}
.bottom-tip .tip-text.data-v-3f6102ab {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}
.action-buttons.data-v-3f6102ab {
  display: flex;
  justify-content: center;
}
.download-btn-small.data-v-3f6102ab {
  background-color: #3b7eeb;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.download-btn-small.data-v-3f6102ab:active {
  transform: scale(0.98);
}

/* 进度指示器 */
.progress-indicator.data-v-3f6102ab {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 10000;
}
.progress-bar.data-v-3f6102ab {
  width: 100%;
  height: 100%;
  position: relative;
}
.progress-fill.data-v-3f6102ab {
  height: 100%;
  background: linear-gradient(90deg, #3b7eeb 0%, #2c62c9 100%);
  transition: width 0.3s ease;
}