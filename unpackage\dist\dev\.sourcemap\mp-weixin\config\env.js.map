{"version": 3, "file": "env.js", "sources": ["config/env.js"], "sourcesContent": ["// 环境配置管理\n// 支持开发、测试、生产环境的API地址配置\n\n// 环境类型枚举\nexport const ENV_TYPE = {\n  DEVELOPMENT: 'development',\n  TESTING: 'testing', \n  PRODUCTION: 'production'\n};\n\n// 环境配置\nconst envConfig = {\n  // 开发环境\n  [ENV_TYPE.DEVELOPMENT]: {\n    name: '开发环境',\n    baseURL: 'http://192.168.1.101:14010',\n    wechatAppId: '', // 微信小程序AppID - 开发环境\n    debug: true,\n    timeout: 10000\n  },\n  \n  // 测试环境  \n  [ENV_TYPE.TESTING]: {\n    name: '测试环境',\n    baseURL: 'https://test-api.example.com',\n    wechatAppId: '', // 微信小程序AppID - 测试环境\n    debug: true,\n    timeout: 15000\n  },\n  \n  // 生产环境\n  [ENV_TYPE.PRODUCTION]: {\n    name: '生产环境', \n    baseURL: 'https://api.example.com',\n    wechatAppId: '', // 微信小程序AppID - 生产环境\n    debug: false,\n    timeout: 20000\n  }\n};\n\n// 当前环境 - 可通过构建工具或手动切换\nlet currentEnv = ENV_TYPE.DEVELOPMENT;\n\n// 自动检测环境（基于域名或其他标识）\nfunction detectEnvironment() {\n  // 在微信小程序中可以通过不同的方式检测环境\n  // 这里提供一个基础的检测逻辑\n  try {\n    const accountInfo = wx.getAccountInfoSync();\n    const envVersion = accountInfo.miniProgram.envVersion;\n    \n    switch (envVersion) {\n      case 'develop':\n        return ENV_TYPE.DEVELOPMENT;\n      case 'trial':\n        return ENV_TYPE.TESTING;\n      case 'release':\n        return ENV_TYPE.PRODUCTION;\n      default:\n        return ENV_TYPE.DEVELOPMENT;\n    }\n  } catch (error) {\n    console.warn('环境检测失败，使用默认开发环境:', error);\n    return ENV_TYPE.DEVELOPMENT;\n  }\n}\n\n// 初始化环境\nfunction initEnvironment() {\n  currentEnv = detectEnvironment();\n  console.log(`当前环境: ${getCurrentConfig().name}`);\n}\n\n// 获取当前环境配置\nexport function getCurrentConfig() {\n  return envConfig[currentEnv];\n}\n\n// 获取当前环境类型\nexport function getCurrentEnv() {\n  return currentEnv;\n}\n\n// 手动设置环境（用于调试）\nexport function setEnvironment(env) {\n  if (envConfig[env]) {\n    currentEnv = env;\n    console.log(`环境已切换到: ${getCurrentConfig().name}`);\n    return true;\n  }\n  console.error('无效的环境类型:', env);\n  return false;\n}\n\n// 获取API基础地址\nexport function getBaseURL() {\n  return getCurrentConfig().baseURL;\n}\n\n// 获取微信AppID\nexport function getWechatAppId() {\n  return getCurrentConfig().wechatAppId;\n}\n\n// 是否为调试模式\nexport function isDebug() {\n  return getCurrentConfig().debug;\n}\n\n// 获取请求超时时间\nexport function getTimeout() {\n  return getCurrentConfig().timeout;\n}\n\n// 获取所有环境配置（用于环境切换界面）\nexport function getAllEnvConfigs() {\n  return Object.keys(envConfig).map(key => ({\n    type: key,\n    ...envConfig[key]\n  }));\n}\n\n// 环境配置验证\nexport function validateConfig() {\n  const config = getCurrentConfig();\n  const errors = [];\n  \n  if (!config.baseURL) {\n    errors.push('API基础地址未配置');\n  }\n  \n  if (!config.wechatAppId) {\n    errors.push('微信AppID未配置');\n  }\n  \n  if (errors.length > 0) {\n    console.warn('环境配置验证失败:', errors);\n    return { valid: false, errors };\n  }\n  \n  return { valid: true, errors: [] };\n}\n\n// 初始化环境配置\ninitEnvironment();\n\n// 导出默认配置\nexport default {\n  getCurrentConfig,\n  getCurrentEnv,\n  setEnvironment,\n  getBaseURL,\n  getWechatAppId,\n  isDebug,\n  getTimeout,\n  getAllEnvConfigs,\n  validateConfig,\n  ENV_TYPE\n};\n"], "names": ["wx", "uni"], "mappings": ";;AAIO,MAAM,WAAW;AAAA,EACtB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AACd;AAGA,MAAM,YAAY;AAAA;AAAA,EAEhB,CAAC,SAAS,WAAW,GAAG;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA;AAAA,IACb,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AAAA;AAAA,EAGD,CAAC,SAAS,OAAO,GAAG;AAAA,IAClB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA;AAAA,IACb,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AAAA;AAAA,EAGD,CAAC,SAAS,UAAU,GAAG;AAAA,IACrB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA;AAAA,IACb,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AACH;AAGA,IAAI,aAAa,SAAS;AAG1B,SAAS,oBAAoB;AAG3B,MAAI;AACF,UAAM,cAAcA,mBAAG;AACvB,UAAM,aAAa,YAAY,YAAY;AAE3C,YAAQ,YAAU;AAAA,MAChB,KAAK;AACH,eAAO,SAAS;AAAA,MAClB,KAAK;AACH,eAAO,SAAS;AAAA,MAClB,KAAK;AACH,eAAO,SAAS;AAAA,MAClB;AACE,eAAO,SAAS;AAAA,IACnB;AAAA,EACF,SAAQ,OAAO;AACdC,kBAAA,MAAA,MAAA,QAAA,uBAAa,oBAAoB,KAAK;AACtC,WAAO,SAAS;AAAA,EACjB;AACH;AAGA,SAAS,kBAAkB;AACzB,eAAa,kBAAiB;AAC9BA,sBAAY,MAAA,OAAA,uBAAA,SAAS,iBAAkB,EAAC,IAAI,EAAE;AAChD;AAGO,SAAS,mBAAmB;AACjC,SAAO,UAAU,UAAU;AAC7B;AAmBO,SAAS,aAAa;AAC3B,SAAO,iBAAkB,EAAC;AAC5B;AAQO,SAAS,UAAU;AACxB,SAAO,iBAAkB,EAAC;AAC5B;AAGO,SAAS,aAAa;AAC3B,SAAO,iBAAkB,EAAC;AAC5B;AAgCA,gBAAiB;;;;"}