// 微信小程序认证工具 - 2024年最新版本
import { getWechatAppId, isDebug } from '@/config/env.js';
import { authUtils } from '@/server/require.js';
import { showWechatError } from '@/utils/wechat-error-handler.js';
import { api } from '@/utils/api.js';

/**
 * 微信登录状态枚举
 */
export const LOGIN_STATUS = {
  NOT_LOGIN: 'not_login',      // 未登录
  LOGGING: 'logging',          // 登录中
  LOGIN_SUCCESS: 'login_success', // 登录成功
  LOGIN_FAILED: 'login_failed'    // 登录失败
};

/**
 * 用户信息获取状态枚举
 */
export const USER_INFO_STATUS = {
  NOT_AUTHORIZED: 'not_authorized',    // 未授权
  AUTHORIZING: 'authorizing',          // 授权中
  AUTHORIZED: 'authorized',            // 已授权
  AUTHORIZATION_FAILED: 'auth_failed'  // 授权失败
};

/**
 * 微信登录工具类 - 支持最新的登录流程和用户信息获取
 */
class WechatAuth {
  constructor() {
    this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
    this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;
    this.userInfo = null;
    this.wechatUserInfo = null; // 通过wx.getUserProfile获取的用户信息
    this.sessionKey = null;
    this.openid = null;
    this.unionid = null;
    this.loginRetryCount = 0;
    this.maxRetryCount = 3;
    this.sessionCheckTimer = null;

    // 启动会话检查定时器
    this.startSessionCheck();
  }

  /**
   * 启动会话检查定时器 - 每30分钟检查一次登录状态
   */
  startSessionCheck() {
    if (this.sessionCheckTimer) {
      clearInterval(this.sessionCheckTimer);
    }

    // 每30分钟检查一次会话状态
    this.sessionCheckTimer = setInterval(() => {
      this.checkLoginStatus().catch(error => {
        if (isDebug()) {
          console.warn('定时会话检查失败:', error);
        }
      });
    }, 30 * 60 * 1000);
  }

  /**
   * 停止会话检查定时器
   */
  stopSessionCheck() {
    if (this.sessionCheckTimer) {
      clearInterval(this.sessionCheckTimer);
      this.sessionCheckTimer = null;
    }
  }

  /**
   * 检查微信登录状态 - 增强版本，支持自动续期
   */
  checkLoginStatus() {
    return new Promise((resolve) => {
      wx.checkSession({
        success: () => {
          // session_key 未过期，并且在本生命周期一直有效
          const access_token = uni.getStorageSync('access_token');
          const tokenExpireTime = uni.getStorageSync('token_expire_time');
          const currentTime = Date.now();

          if (access_token && (!tokenExpireTime || currentTime < tokenExpireTime)) {
            this.loginStatus = LOGIN_STATUS.LOGIN_SUCCESS;
            resolve({
              isLogin: true,
              needReLogin: false,
              tokenValid: true
            });
          } else {
            // token过期，需要重新登录
            this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
            resolve({
              isLogin: false,
              needReLogin: true,
              tokenValid: false,
              reason: 'token_expired'
            });
          }
        },
        fail: (error) => {
          // session_key 已经失效，需要重新执行登录流程
          this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
          if (isDebug()) {
            console.log('微信会话已失效:', error);
          }
          resolve({
            isLogin: false,
            needReLogin: true,
            tokenValid: false,
            reason: 'session_expired'
          });
        }
      });
    });
  }

  /**
   * 微信登录 - 增强版本，支持完整的用户授权流程
   * @param {Object} options 登录选项
   * @returns {Promise}
   */
  login(options = {}) {
    return new Promise((resolve, reject) => {
      if (this.loginStatus === LOGIN_STATUS.LOGGING) {
        reject(new Error('正在登录中，请勿重复操作'));
        return;
      }

      // 重置重试计数
      this.loginRetryCount = 0;
      this._performLogin(options, resolve, reject);
    });
  }

  /**
   * 执行登录操作（内部方法，支持重试和完整流程）
   */
  _performLogin(options, resolve, reject) {
    this.loginStatus = LOGIN_STATUS.LOGGING;

    // 设置超时时间，默认15秒（增加时间以支持用户授权）
    const timeout = options.timeout || 15000;
    const timeoutTimer = setTimeout(() => {
      this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;
      reject(new Error('登录超时，请检查网络连接'));
    }, timeout);

    // 优化后的登录流程：先获取用户信息授权，再进行登录
    this._getUserProfileWithFallback(options)
      .then((userProfileResult) => {
        if (isDebug()) {
          console.log('用户信息获取完成:', userProfileResult);
        }

        // 获取微信登录code
        return this._getWechatLoginCode();
      })
      .then((code) => {
        clearTimeout(timeoutTimer);
        
        if (isDebug()) {
          console.log('微信登录获取code成功:', code);
        }

        // 调用后端接口完成登录
        return this.exchangeSessionKey(code, options);
      })
      .then(resolve)
      .catch((error) => {
        clearTimeout(timeoutTimer);
        
        // 如果是网络错误且未达到最大重试次数，则重试
        if (this.loginRetryCount < this.maxRetryCount &&
            (error.message.includes('网络') || error.message.includes('超时'))) {
          this.loginRetryCount++;
          if (isDebug()) {
            console.log(`登录失败，正在重试 (${this.loginRetryCount}/${this.maxRetryCount}):`, error.message);
          }
          setTimeout(() => {
            this._performLogin(options, resolve, reject);
          }, 1000 * this.loginRetryCount); // 递增延迟重试
        } else {
          this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;
          reject(error);
        }
      });
  }

  /**
   * 获取用户信息授权（支持降级处理）
   * @param {Object} options 选项
   * @returns {Promise}
   */
  _getUserProfileWithFallback(options = {}) {
    return new Promise((resolve) => {
      // 检查是否支持getUserProfile API
      if (typeof wx.getUserProfile === 'function') {
        // 显示授权说明
        if (options.showAuthTip !== false) {
          uni.showModal({
            title: '用户信息授权',
            content: '为了提供更好的服务体验，需要获取您的微信头像和昵称信息，该信息仅用于个人账户展示',
            confirmText: '同意授权',
            cancelText: '暂不授权',
            success: (modalRes) => {
              if (modalRes.confirm) {
                this._callGetUserProfile(resolve);
              } else {
                // 用户拒绝授权，但继续登录流程
                if (isDebug()) {
                  console.log('用户拒绝授权，继续登录流程');
                }
                this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
                resolve({ 
                  success: false, 
                  reason: 'user_denied',
                  message: '用户拒绝授权，已跳过用户信息获取',
                  shouldContinueLogin: true // 标记应该继续登录流程
                });
              }
            },
            fail: () => {
              // Modal显示失败，继续登录流程（不再尝试获取用户信息）
              if (isDebug()) {
                console.log('授权弹窗显示失败，继续登录流程');
              }
              this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
              resolve({ 
                success: false, 
                reason: 'modal_failed',
                message: '授权弹窗显示失败，已跳过用户信息获取',
                shouldContinueLogin: true
              });
            }
          });
        } else {
          // 直接调用getUserProfile
          this._callGetUserProfile(resolve);
        }
      } else {
        // API不可用，跳过用户信息获取，继续登录流程
        if (isDebug()) {
          console.log('getUserProfile API不可用，跳过用户信息获取');
        }
        this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;
        resolve({ 
          success: false, 
          reason: 'api_unavailable',
          message: 'getUserProfile API不可用',
          shouldContinueLogin: true
        });
      }
    });
  }

  /**
   * 调用getUserProfile API
   * @param {Function} resolve Promise resolve函数
   */
  _callGetUserProfile(resolve) {
    this.userInfoStatus = USER_INFO_STATUS.AUTHORIZING;
    
    wx.getUserProfile({
      desc: '用于完善会员资料', // 必填，说明获取用户信息的用途
      success: (res) => {
        if (isDebug()) {
          console.log('获取用户信息成功:', res.userInfo);
        }

        this.wechatUserInfo = res.userInfo;
        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;
        
        // 保存用户信息到本地
        uni.setStorageSync('wechat_userInfo', res.userInfo);

        resolve({
          success: true,
          userInfo: res.userInfo,
          message: '用户信息获取成功'
        });
      },
      fail: (error) => {
        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
        
        if (isDebug()) {
          console.error('getUserProfile失败:', error);
        }

        // 用户拒绝或其他错误，继续登录流程
        resolve({
          success: false,
          reason: 'get_profile_failed',
          error: error.errMsg,
          message: '获取用户信息失败，但继续登录流程',
          shouldContinueLogin: true // 继续登录流程
        });
      }
    });
  }

  /**
   * 获取微信登录code
   * @returns {Promise<string>}
   */
  _getWechatLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (loginRes) => {
          if (loginRes.code) {
            resolve(loginRes.code);
          } else {
            reject(new Error('微信登录失败：' + loginRes.errMsg));
          }
        },
        fail: (error) => {
          reject(new Error('微信登录失败：' + error.errMsg));
        }
      });
    });
  }

  /**
   * 向后端换取session_key - 增强版本，支持用户信息传递
   * @param {string} code 微信登录code
   * @param {Object} options 选项
   */
  exchangeSessionKey(code, options = {}) {
    return new Promise((resolve, reject) => {
      const requestTimeout = options.requestTimeout || 8000;

      // 准备请求数据
      let requestData = {
        js_code: code
      };

      // 如果获取到了用户信息，则一并传递（使用用户修改后的格式）
      if (this.wechatUserInfo) {
        requestData.nickname = this.wechatUserInfo.nickName;
        requestData.avatar_url = this.wechatUserInfo.avatarUrl;
        if (isDebug()) {
          console.log(requestData,'包含用户信息的登录请求:', {
            js_code: code,
            hasUserInfo: true,
            userInfo: this.wechatUserInfo
          });
        }
      } else {
        if (isDebug()) {
          console.log('仅包含code的登录请求:', { js_code: code });
        }
      }

      // 调用后端API换取session_key - 使用统一的API管理
      api.wechat.login(requestData)
        .then((responseData) => {
          if (isDebug()) {
            console.log('后端登录响应:', responseData);
          }

          try {
            // API统一管理已处理状态码和基础错误，这里直接处理业务数据
            const { access_token, token_type, userInfo, openid, unionid, sessionKey, expiresIn, refresh_token } = responseData;

            // 重要：无论是否获取到微信用户信息，都要保存登录凭证
            // 使用统一的token管理工具保存登录信息
            if (access_token) {
              authUtils.setTokenInfo({
                access_token,
                token_type,
                expires_in: expiresIn
              });
            }

            // 保存refresh_token（如果后端返回了的话）
            if (refresh_token) {
              authUtils.setRefreshToken(refresh_token);
            }

            // 保存其他登录信息（包括openid）
            this.saveLoginInfo({
              access_token,
              token_type,
              userInfo,
              openid,
              unionid,
              sessionKey,
              expiresIn
            });

            this.loginStatus = LOGIN_STATUS.LOGIN_SUCCESS;

            // 显示适当的成功提示
            if (this.wechatUserInfo) {
              uni.showToast({
                title: '登录成功',
                icon: 'success',
                duration: 2000
              });
            } else {
              uni.showToast({
                title: '登录成功，建议完善个人信息',
                icon: 'none',
                duration: 3000
              });
            }

            resolve({
              success: true,
              access_token,
              token_type,
              userInfo,
              openid,
              unionid,
              expiresIn,
              refresh_token,
              wechatUserInfo: this.wechatUserInfo // 可能为null，但这是正常的
            });
          } catch (error) {
            // 处理数据解析错误
            if (isDebug()) {
              console.error('登录数据解析错误:', error);
            }
            this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;
            reject(new Error('登录数据处理失败'));
          }
        })
        .catch((error) => {
          this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;

          if (isDebug()) {
            console.error('微信登录API调用失败:', error);
          }

          // API统一管理已处理网络错误，这里直接传递错误信息
          reject(error);
        });
    });
  }

  /**
   * 获取用户信息（保留原有方法，增加更好的错误提示）
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      // 检查是否支持getUserProfile
      if (typeof wx.getUserProfile === 'function') {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: (res) => {
            if (isDebug()) {
              console.log('获取用户信息成功:', res.userInfo);
            }

            this.wechatUserInfo = res.userInfo;
            this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;
            // 保存用户信息到本地
            uni.setStorageSync('wechat_userInfo', res.userInfo);

            resolve(res.userInfo);
          },
          fail: (error) => {
            this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;
            
            // 根据错误类型提供更友好的提示
            let errorMessage = '获取用户信息失败';
            if (error.errMsg && error.errMsg.includes('deny')) {
              errorMessage = '用户拒绝授权获取信息';
            } else if (error.errMsg && error.errMsg.includes('auth')) {
              errorMessage = '用户信息授权失败';
            }
            
            if (isDebug()) {
              console.error('getUserProfile失败:', error);
            }
            reject(new Error(errorMessage));
          }
        });
      } else {
        // API不可用，提示使用新的头像昵称填写能力
        reject(new Error('当前微信版本不支持getUserProfile，请升级微信版本或使用其他登录方式'));
      }
    });
  }

  /**
   * 处理头像选择 - 新的头像昵称填写能力
   * @param {Object} event 头像选择事件对象
   * @returns {Promise}
   */
  handleChooseAvatar(event) {
    return new Promise((resolve, reject) => {
      try {
        const { avatarUrl } = event.detail;
        if (!avatarUrl) {
          reject(new Error('未获取到头像信息'));
          return;
        }

        if (isDebug()) {
          console.log('用户选择头像:', avatarUrl);
        }

        // 更新本地用户信息
        const currentUserInfo = this.wechatUserInfo || {};
        const updatedUserInfo = {
          ...currentUserInfo,
          avatarUrl: avatarUrl,
          updateTime: Date.now()
        };

        this.wechatUserInfo = updatedUserInfo;
        uni.setStorageSync('wechat_userInfo', updatedUserInfo);

        resolve({
          success: true,
          avatarUrl: avatarUrl,
          userInfo: updatedUserInfo
        });
      } catch (error) {
        if (isDebug()) {
          console.error('处理头像选择失败:', error);
        }
        reject(new Error('处理头像选择失败：' + error.message));
      }
    });
  }

  /**
   * 处理昵称输入 - 新的头像昵称填写能力
   * @param {string} nickname 用户输入的昵称
   * @returns {Promise}
   */
  handleNicknameInput(nickname) {
    return new Promise((resolve, reject) => {
      try {
        if (!nickname || nickname.trim().length === 0) {
          reject(new Error('昵称不能为空'));
          return;
        }

        // 昵称长度限制
        if (nickname.length > 20) {
          reject(new Error('昵称长度不能超过20个字符'));
          return;
        }

        if (isDebug()) {
          console.log('用户输入昵称:', nickname);
        }

        // 更新本地用户信息
        const currentUserInfo = this.wechatUserInfo || {};
        const updatedUserInfo = {
          ...currentUserInfo,
          nickName: nickname.trim(),
          updateTime: Date.now()
        };

        this.wechatUserInfo = updatedUserInfo;
        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;
        uni.setStorageSync('wechat_userInfo', updatedUserInfo);

        resolve({
          success: true,
          nickName: nickname.trim(),
          userInfo: updatedUserInfo
        });
      } catch (error) {
        if (isDebug()) {
          console.error('处理昵称输入失败:', error);
        }
        reject(new Error('处理昵称输入失败：' + error.message));
      }
    });
  }

  /**
   * 保存登录信息到本地存储 - 增强版本，支持过期时间管理
   */
  saveLoginInfo({ access_token, token_type, userInfo, openid, unionid, sessionKey, expiresIn }) {
    try {
      const currentTime = Date.now();

      if (access_token) {
        uni.setStorageSync('access_token', access_token);

        // 保存token类型（默认为Bearer）
        const tokenType = token_type || 'Bearer';
        uni.setStorageSync('token_type', tokenType);

        // 计算token过期时间（默认7天）
        const expireTime = expiresIn ?
          currentTime + (expiresIn * 1000) :
          currentTime + (7 * 24 * 60 * 60 * 1000);
        uni.setStorageSync('token_expire_time', expireTime);
      }

      if (userInfo) {
        uni.setStorageSync('userInfo', userInfo);
        this.userInfo = userInfo;
      }

      if (openid) {
        uni.setStorageSync('openid', openid);
        this.openid = openid;
      }

      if (unionid) {
        uni.setStorageSync('unionid', unionid);
        this.unionid = unionid;
      }

      if (sessionKey) {
        this.sessionKey = sessionKey;
        // 不保存sessionKey到本地存储，仅保存在内存中
      }

      // 保存登录时间
      uni.setStorageSync('login_time', currentTime);

      if (isDebug()) {
        console.log('登录信息已保存到本地存储', {
          hasToken: !!access_token,
          hasUserInfo: !!userInfo,
          hasOpenid: !!openid,
          hasUnionid: !!unionid,
          expiresIn: expiresIn
        });
      }
    } catch (error) {
      console.error('保存登录信息失败:', error);
      throw new Error('保存登录信息失败');
    }
  }

  /**
   * 清除登录信息 - 增强版本
   */
  clearLoginInfo() {
    try {
      // 停止会话检查定时器
      this.stopSessionCheck();

      // 清除所有存储的登录相关信息
      const keysToRemove = [
        'access_token',
        'token_type',
        'token_expire_time',
        'userInfo',
        'openid',
        'unionid',
        'wechat_userInfo',
        'login_time'
      ];

      keysToRemove.forEach(key => {
        try {
          uni.removeStorageSync(key);
        } catch (e) {
          if (isDebug()) {
            console.warn(`清除存储项 ${key} 失败:`, e);
          }
        }
      });

      // 重置内存中的状态
      this.loginStatus = LOGIN_STATUS.NOT_LOGIN;
      this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;
      this.userInfo = null;
      this.wechatUserInfo = null;
      this.sessionKey = null;
      this.openid = null;
      this.unionid = null;
      this.loginRetryCount = 0;

      if (isDebug()) {
        console.log('登录信息已完全清除');
      }
    } catch (error) {
      console.error('清除登录信息失败:', error);
      throw new Error('清除登录信息失败');
    }
  }

  /**
   * 获取当前登录状态
   */
  getLoginStatus() {
    return {
      loginStatus: this.loginStatus,
      userInfoStatus: this.userInfoStatus
    };
  }

  /**
   * 获取存储的用户信息 - 增强版本，包含完整性检查
   */
  getStoredUserInfo() {
    try {
      const userInfo = uni.getStorageSync('userInfo');
      const wechatUserInfo = uni.getStorageSync('wechat_userInfo');
      const loginTime = uni.getStorageSync('login_time');
      const tokenExpireTime = uni.getStorageSync('token_expire_time');

      return {
        userInfo,
        wechatUserInfo,
        loginTime,
        tokenExpireTime,
        isTokenValid: tokenExpireTime ? Date.now() < tokenExpireTime : false,
        hasCompleteUserInfo: !!(wechatUserInfo && wechatUserInfo.nickName && wechatUserInfo.avatarUrl)
      };
    } catch (error) {
      console.error('获取存储的用户信息失败:', error);
      return {
        userInfo: null,
        wechatUserInfo: null,
        loginTime: null,
        tokenExpireTime: null,
        isTokenValid: false,
        hasCompleteUserInfo: false
      };
    }
  }

  /**
   * 获取完整的用户数据对象
   */
  getCompleteUserData() {
    const storedInfo = this.getStoredUserInfo();
    const openid = this.getOpenid();
    const unionid = this.getUnionid();

    return {
      // 基础登录信息
      openid,
      unionid,
      loginTime: storedInfo.loginTime,
      tokenExpireTime: storedInfo.tokenExpireTime,
      isTokenValid: storedInfo.isTokenValid,

      // 后端返回的用户信息
      userInfo: storedInfo.userInfo,

      // 微信用户信息（通过头像昵称填写能力获取）
      wechatUserInfo: storedInfo.wechatUserInfo,

      // 状态信息
      loginStatus: this.loginStatus,
      userInfoStatus: this.userInfoStatus,
      hasCompleteUserInfo: storedInfo.hasCompleteUserInfo
    };
  }

  /**
   * 获取openid
   */
  getOpenid() {
    return this.openid || uni.getStorageSync('openid');
  }

  /**
   * 获取unionid
   */
  getUnionid() {
    return this.unionid || uni.getStorageSync('unionid');
  }

  /**
   * 检查是否需要获取用户信息
   */
  needUserInfo() {
    const { hasCompleteUserInfo } = this.getStoredUserInfo();
    return !hasCompleteUserInfo;
  }

  /**
   * 销毁实例（清理资源）
   */
  destroy() {
    this.stopSessionCheck();
    this.clearLoginInfo();
  }
}

// 创建单例实例
const wechatAuth = new WechatAuth();

// 导出实例和工具函数
export default wechatAuth;

/**
 * 快捷登录函数 - 支持完整的登录流程
 */
export function quickLogin(options = {}) {
  return wechatAuth.login(options);
}

/**
 * 检查登录状态
 */
export function checkLogin() {
  return wechatAuth.checkLoginStatus();
}

/**
 * 退出登录 - 完整清理
 */
export function logout() {
  wechatAuth.clearLoginInfo();
  return Promise.resolve({ success: true });
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return wechatAuth.getStoredUserInfo();
}

/**
 * 获取完整用户数据
 */
export function getCompleteUserData() {
  return wechatAuth.getCompleteUserData();
}

/**
 * 处理头像选择
 */
export function handleChooseAvatar(event) {
  return wechatAuth.handleChooseAvatar(event);
}

/**
 * 处理昵称输入
 */
export function handleNicknameInput(nickname) {
  return wechatAuth.handleNicknameInput(nickname);
}

/**
 * 检查是否需要获取用户信息
 */
export function needUserInfo() {
  return wechatAuth.needUserInfo();
}

/**
 * 获取用户信息状态
 */
export function getUserInfoStatus() {
  return wechatAuth.userInfoStatus;
}

/**
 * 自动登录检查 - 检查登录状态并在需要时自动登录
 */
export function autoLoginCheck(options = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const loginStatus = await wechatAuth.checkLoginStatus();

      if (loginStatus.isLogin) {
        // 已登录，返回用户数据
        resolve({
          isLogin: true,
          userData: wechatAuth.getCompleteUserData(),
          needUserInfo: wechatAuth.needUserInfo()
        });
      } else if (options.autoLogin !== false) {
        // 自动重新登录
        try {
          const loginResult = await wechatAuth.login(options);
          resolve({
            isLogin: true,
            userData: wechatAuth.getCompleteUserData(),
            needUserInfo: wechatAuth.needUserInfo(),
            autoLoginSuccess: true
          });
        } catch (loginError) {
          resolve({
            isLogin: false,
            needLogin: true,
            autoLoginFailed: true,
            error: loginError.message
          });
        }
      } else {
        // 不自动登录
        resolve({
          isLogin: false,
          needLogin: true,
          reason: loginStatus.reason
        });
      }
    } catch (error) {
      reject(error);
    }
  });
}

// 状态枚举已在文件开头导出，这里不需要重复导出
