{"version": 3, "file": "contact_information.js", "sources": ["pages/contact_information/contact_information.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY29udGFjdF9pbmZvcm1hdGlvbi9jb250YWN0X2luZm9ybWF0aW9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <view class=\"gradient-bg card\">\r\n      <view class=\"icon-box\">\r\n        <i class=\"fas fa-envelope\"></i>\r\n      </view>\r\n      <h2 class=\"text\">确认联系方式</h2>\r\n      <view class=\"text-gray\">为了及时向您发送调解结果短信，便于日后快速查找相关信息，请确认您的手机号码</view>\r\n    </view>\r\n\r\n    <view class=\"card form-container\">\r\n      <h3 class=\"text-lg\">确认联系方式</h3>\r\n      <view class=\"form-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">手机号码</text>\r\n        </view>\r\n        <view class=\"card-content\">\r\n          <uni-easyinput\r\n            v-model=\"formData.phoneNumber\"\r\n            placeholder=\"请输入手机号\"\r\n            class=\"input-field\"\r\n          >\r\n          </uni-easyinput>\r\n        </view>\r\n        <view class=\"text-xs\"><i class=\"fas fa-info-circle\"></i>此手机号仅用于发送调解结果短信，不会用于其他用途</view>\r\n      </view>\r\n      <view class=\"form-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">短信验证码</text>\r\n        </view>\r\n        <view class=\"card-content code-input\">\r\n          <uni-easyinput\r\n            v-model=\"formData.verificationCode\"\r\n            placeholder=\"请输入验证码\"\r\n            class=\"input-field\"\r\n          >\r\n          </uni-easyinput>\r\n          <button\r\n            @click=\"getVerificationCode\"\r\n            :disabled=\"countdown > 0\"\r\n            class=\"code-btn\"\r\n            :class=\"\r\n              countdown > 0\r\n                ? 'bg-gray-200 text-gray-500'\r\n                : 'bg-blue-100 text-blue-600 hover:bg-blue-200'\r\n            \"\r\n          >\r\n            {{ countdown > 0 ? `${countdown}s后重新获取` : \"获取验证码\" }}\r\n          </button>\r\n        </view>\r\n      </view>\r\n      <!-- <view class=\"mb-4\">\r\n        <label for=\"phone\" class=\"block text-sm font-medium text-gray-700 mb-1\">手机号码</label>\r\n        <input \r\n        type=\"tel\" \r\n        id=\"phone\" \r\n        v-model=\"phoneNumber\" \r\n        placeholder=\"请输入您的手机号码\" \r\n        class=\"w-full\"\r\n        >\r\n        <view class=\"text-xs\"><i class=\"fas fa-info-circle\"></i>此手机号仅用于发送调解结果短信，不会用于其他用途</view>\r\n      </view>\r\n      \r\n      <view class=\"mb-6\">\r\n        <label for=\"code\" class=\"block text-sm font-medium text-gray-700 mb-1\">短信验证码</label>\r\n        <view class=\"flex gap-2\">\r\n        <input \r\n          type=\"text\" \r\n          id=\"code\" \r\n          v-model=\"verificationCode\" \r\n          placeholder=\"请输入验证码\" \r\n          class=\"flex-1\"\r\n        >\r\n        <button \r\n          @click=\"getVerificationCode\" \r\n          :disabled=\"countdown > 0\"\r\n          class=\"px-4 py-3 rounded-lg text-sm font-medium whitespace-nowrap\"\r\n          :class=\"countdown > 0 ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'\"\r\n        >\r\n          {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}\r\n        </button>\r\n\t\t\t</view> \r\n\t\t</view>-->\r\n    </view>\r\n    <view class=\"action-buttons\">\r\n      <button @click=\"confirmContact\" class=\"confirm-btn\">\r\n        <i class=\"fas fa-check\"></i>确认联系方式\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, createApp, reactive, computed, onMounted } from \"vue\";\r\nimport { onPullDownRefresh } from \"@dcloudio/uni-app\";\r\nimport { api } from \"@/utils/api.js\";\r\n// 表单数据\r\nconst formData = reactive({\r\n  phoneNumber: \"18698990903\", // 手机号码\r\n  verificationCode: \"557722\", // 验证码\r\n});\r\nconst countdown = ref(0); // 验证码倒计时\r\nconst isSubmitting = ref(false); // 提交状态\r\n// 模拟获取验证码\r\nconst getVerificationCode = () => {\r\n  console.log(formData.phoneNumber.value,'=====',formData.phoneNumber);\r\n  if (!formData.phoneNumber) {\r\n    uni.showToast({\r\n      title: '请输入手机号码',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n\r\n  // 简单的手机号验证\r\n  if (!/^1[3-9]\\d{9}$/.test(formData.phoneNumber)) {\r\n    uni.showToast({\r\n      title: '请输入正确的手机号码',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n\r\n  // 模拟API调用 - 实际开发中替换为真实的API调用\r\n  console.log(\"调用发送验证码API，手机号:\", formData.phoneNumber);\r\n\r\n  // 开始倒计时\r\n  countdown.value = 60;\r\n  const timer = setInterval(() => {\r\n    countdown.value--;\r\n    if (countdown.value <= 0) {\r\n      clearInterval(timer);\r\n    }\r\n  }, 1000);\r\n\r\n  // 模拟API返回 - 实际开发中处理真实的API响应\r\n  setTimeout(() => {\r\n    console.log(\"验证码已发送\");\r\n    // 这里可以添加提示，如Toast通知\r\n  }, 1000);\r\n};\r\n\r\n// 模拟确认联系方式\r\nconst confirmContact = () => {\r\n  if (!formData.phoneNumber) {\r\n    uni.showToast({\r\n      title: '请输入手机号码',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n\r\n  if (!formData.verificationCode) {\r\n    uni.showToast({\r\n      title: '请输入验证码',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n\r\n  // 简单的验证码验证\r\n  if (formData.verificationCode.length !== 6) {\r\n    uni.showToast({\r\n      title: '请输入6位验证码',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n\r\n  isSubmitting.value = true;\r\n\r\n  // 模拟API调用 - 实际开发中替换为真实的API调用\r\n  console.log(\"调用确认联系方式API\", {\r\n    phone: formData.phoneNumber,\r\n    code: formData.verificationCode,\r\n  });\r\n\r\n  // 模拟API响应 - 实际开发中处理真实的API响应\r\n  // setTimeout(() => {\r\n    isSubmitting.value = false;\r\n    uni.showToast({\r\n      title: '联系方式确认成功！',\r\n      icon: 'none'\r\n    });\r\n  // }, 1500);\r\n  \r\n    // 这里可以添加跳转逻辑，如跳转到下一个页面\r\n    uni.navigateTo({\r\n      url: '/pages/case_completed/case_completed',\r\n      success: () => {\r\n        console.log('页面跳转成功');\r\n      },\r\n      fail: (error) => {\r\n        console.error('页面跳转失败：', error);\r\n        uni.showToast({\r\n          title: '页面跳转失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      }\r\n   });\r\n};\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  console.log(\"调解查询页面已加载\");\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:root {\r\n  --primary-color: #3b7eeb;\r\n  --primary-light: #e6f0ff;\r\n  --transition-normal: all 0.3s ease;\r\n  --text-secondary: #666;\r\n  --text-color: #333;\r\n  --text-light: #999;\r\n}\r\n.content {\r\n  height: calc(100% - 188rpx);\r\n  overflow-y: auto;\r\n  padding: 30rpx;\r\n  padding-bottom: 140rpx;\r\n  background-color: #f8fafc;\r\n}\r\n.gradient-bg {\r\n  background: linear-gradient(135deg, var(--primary-light) 0%, #e6f0ff 100%);\r\n  border: none;\r\n  text-align: center;\r\n  padding: 60rpx 40rpx;\r\n}\r\n.icon-box {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  background-color: var(--primary-color);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 40rpx;\r\n  .fas {\r\n    color: white;\r\n    font-size: 48rpx;\r\n  }\r\n}\r\n.text {\r\n  margin: 0 0 20rpx 0;\r\n  color: var(--primary-color);\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n}\r\n.text-gray {\r\n  color: var(--text-secondary);\r\n  font-size: 28rpx;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n.card {\r\n  background-color: white;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n  padding: 36rpx;\r\n  margin-bottom: 30rpx;\r\n  transition: var(--transition-normal);\r\n  border: 1px solid rgba(0, 0, 0, 0.03);\r\n}\r\n.form-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  .form-card {\r\n    overflow: hidden;\r\n    margin-bottom: 20rpx;\r\n    .card-header {\r\n      padding: 30rpx 30rpx 20rpx 10rpx;\r\n      // border-bottom: 1rpx solid #f0f0f0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .fas {\r\n        margin-right: 8px;\r\n        color: var(--primary-color);\r\n      }\r\n\r\n      .card-title {\r\n        font-size: 30rpx;\r\n        color: var(--text-primary);\r\n        font-weight: 800;\r\n        // flex: 1;\r\n      }\r\n    }\r\n  }\r\n}\r\n.text-lg {\r\n  text-align: center;\r\n  color: var(--text-color);\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n}\r\n.action-buttons {\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.confirm-btn {\r\n  background-color: #3b7eeb;\r\n  color: #fff;\r\n  width: 100%;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  text-align: center;\r\n  border-radius: 16rpx;\r\n  font-size: 32rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  .fas {\r\n    margin-right: 20rpx;\r\n    font-size: 36rpx;\r\n    color: var(--bg-primary);\r\n  }\r\n  &:hover {\r\n    background-color: #2a6ed8;\r\n  }\r\n}\r\n.text-xs {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 25rpx;\r\n  font-size: 24rpx;\r\n  color: var(--text-light);\r\n  .fas {\r\n    color: var(--text-light);\r\n    font-size: 24rpx;\r\n    margin-right: 10rpx;\r\n  }\r\n}\r\n.code-input{\r\n  display: flex;\r\n  gap: 24rpx;\r\n  .input-field{\r\n    position: relative;\r\n    flex: 1;\r\n  }\r\n  .code-btn{\r\n    white-space: nowrap;\r\n    min-width: 100px;\r\n    background-color: var(--primary-color);\r\n    color: white;\r\n  }\r\n}\r\n:deep(.uni-easyinput__content-input) {\r\n  height: 80rpx;\r\n  font-size: 28rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/contact_information/contact_information.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "uni", "onMounted"], "mappings": ";;;;;;;;;;;;;;AAiGA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,aAAa;AAAA;AAAA,MACb,kBAAkB;AAAA;AAAA,IACpB,CAAC;AACD,UAAM,YAAYC,cAAAA,IAAI,CAAC;AACvB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAE9B,UAAM,sBAAsB,MAAM;AAChCC,oBAAAA,MAAY,MAAA,OAAA,4DAAA,SAAS,YAAY,OAAM,SAAQ,SAAS,WAAW;AACnE,UAAI,CAAC,SAAS,aAAa;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,UAAI,CAAC,gBAAgB,KAAK,SAAS,WAAW,GAAG;AAC/CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAY,MAAA,MAAA,OAAA,4DAAA,mBAAmB,SAAS,WAAW;AAGnD,gBAAU,QAAQ;AAClB,YAAM,QAAQ,YAAY,MAAM;AAC9B,kBAAU;AACV,YAAI,UAAU,SAAS,GAAG;AACxB,wBAAc,KAAK;AAAA,QACpB;AAAA,MACF,GAAE,GAAI;AAGP,iBAAW,MAAM;AACfA,sBAAAA,MAAA,MAAA,OAAA,4DAAY,QAAQ;AAAA,MAErB,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,SAAS,aAAa;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,SAAS,kBAAkB;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,UAAI,SAAS,iBAAiB,WAAW,GAAG;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,mBAAa,QAAQ;AAGrBA,oBAAAA,MAAY,MAAA,OAAA,4DAAA,eAAe;AAAA,QACzB,OAAO,SAAS;AAAA,QAChB,MAAM,SAAS;AAAA,MACnB,CAAG;AAIC,mBAAa,QAAQ;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACZ,CAAK;AAIDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,SAAS,MAAM;AACbA,wBAAAA,MAAY,MAAA,OAAA,4DAAA,QAAQ;AAAA,QACrB;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,wBAAc,MAAA,MAAA,SAAA,4DAAA,WAAW,KAAK;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAChB,CAAS;AAAA,QACF;AAAA,MACP,CAAI;AAAA,IACJ;AAEAC,kBAAAA,UAAU,MAAM;AACdD,oBAAAA,MAAA,MAAA,OAAA,4DAAY,WAAW;AAAA,IACzB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AC3MD,GAAG,WAAW,eAAe;"}