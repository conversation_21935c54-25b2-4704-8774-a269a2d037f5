// 微信登录辅助工具 - 2024年增强版本
import { getBaseURL } from '@/config/env.js';
import wechatAuth, { 
  quickLogin, 
  needUserInfo,
  getUserInfo,
  getCompleteUserData,
  autoLoginCheck,
  LOGIN_STATUS,
  USER_INFO_STATUS
} from '@/utils/wechat-auth.js';
import userStore from '@/utils/user-store.js';  // 修复：使用默认导入
import { withErrorBoundary, ERROR_TYPES, showError } from '@/utils/error-handler.js';
import { withWechatErrorHandling, showWechatError } from '@/utils/wechat-error-handler.js';

/**
 * 登录结果类型枚举
 */
export const LOGIN_RESULT_TYPE = {
  SUCCESS: 'success',          // 登录成功
  NEED_USER_INFO: 'need_user_info', // 需要完善用户信息
  FAILED: 'failed',           // 登录失败
  CANCELLED: 'cancelled'      // 用户取消
};

/**
 * 执行完整的微信登录流程 - 增强版本，支持用户信息授权
 * @param {Object} options 登录选项
 * @returns {Promise<Object>} 登录结果
 */
export async function performWechatLogin(options = {}) {
  const defaultOptions = {
    baseURL: getBaseURL(),
    timeout: 15000, // 增加超时时间以支持用户授权流程
    showLoading: true,
    autoNavigate: true,
    enableRetry: true,
    maxRetries: 2,
    showAuthTip: true, // 是否显示用户授权说明
    ...options
  };

  const loginOperation = async () => {
    if (defaultOptions.showLoading) {
      uni.showLoading({
        title: '登录中...',
        mask: true
      });
    }

    try {
      // 执行优化后的微信登录流程（包含用户信息授权）
      const loginResult = await wechatAuth.login({
        baseURL: defaultOptions.baseURL,
        timeout: defaultOptions.timeout,
        showAuthTip: defaultOptions.showAuthTip
      });

      if (loginResult.success) {
        // 更新用户状态管理
        userStore.setUserInfo(loginResult.userInfo);

        // 获取完整用户数据
        const completeUserData = wechatAuth.getCompleteUserData();

        // 检查是否需要完善用户信息（基于微信用户信息的获取情况）
        const needsUserInfo = !loginResult.wechatUserInfo || needUserInfo();

        const result = {
          type: needsUserInfo ? LOGIN_RESULT_TYPE.NEED_USER_INFO : LOGIN_RESULT_TYPE.SUCCESS,
          success: true,
          userInfo: loginResult.userInfo,
          wechatUserInfo: loginResult.wechatUserInfo, // 新增：微信用户信息
          completeUserData,
          needsUserInfo,
          openid: loginResult.openid,
          unionid: loginResult.unionid,
          hasWechatInfo: !!loginResult.wechatUserInfo // 新增：是否获取到微信用户信息
        };

        // 自动导航
        if (defaultOptions.autoNavigate) {
          await handleLoginNavigation(result);
        }

        return result;
      } else {
        throw new Error('登录失败');
      }
    } finally {
      if (defaultOptions.showLoading) {
        uni.hideLoading();
      }
    }
  };

  // 简化错误处理，避免多层包装导致成功时也触发错误处理
  try {
    let result;
    if (defaultOptions.enableRetry) {
      result = await withWechatErrorHandling(loginOperation, {
        maxRetries: defaultOptions.maxRetries,
        showError: false, // 我们稍后统一处理错误显示
        onRetry: (attempt, error, delay) => {
          uni.showToast({
            title: `登录失败，${Math.round(delay/1000)}秒后重试...`,
            icon: 'none',
            duration: Math.min(delay, 3000)
          });
        }
      });
    } else {
      result = await loginOperation();
    }
    
    return result;
  } catch (error) {
    // 使用微信专用的错误处理显示友好提示
    showWechatError(error);

    return {
      type: LOGIN_RESULT_TYPE.FAILED,
      success: false,
      error: error.message || '登录失败',
      errorType: ERROR_TYPES.UNKNOWN
    };
  }

  return result;
}

/**
 * 处理登录成功后的导航逻辑 - 增强版本，支持微信用户信息状态判断
 * @param {Object} loginResult 登录结果
 */
export async function handleLoginNavigation(loginResult) {
  try {
    // 获取当前页面栈
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';

    // 记录登录成功的操作日志
    try {
      await recordLoginOperation({
        action: 'login_success',
        loginType: 'wechat_miniprogram',
        hasWechatInfo: loginResult.hasWechatInfo,
        needsUserInfo: loginResult.needsUserInfo,
        route: currentRoute
      });
    } catch (error) {
      console.warn('记录登录操作日志失败:', error);
    }

    // 根据登录结果类型和用户信息完整性进行导航
    if (loginResult.type === LOGIN_RESULT_TYPE.NEED_USER_INFO) {
      // 需要完善用户信息，优先跳转到信息完善页面
      if (!loginResult.hasWechatInfo) {
        // 没有获取到微信用户信息，提示用户
        uni.showToast({
          title: '建议完善头像昵称信息以获得更好体验',
          icon: 'none',
          duration: 3000
        });
      }
      
      // 检查是否有信息完善页面
      try {
        uni.navigateTo({
          url: '/pages/profile/complete-info'
        });
        return;
      } catch (error) {
        // 没有信息完善页面，跳转到个人中心
        console.log('没有找到信息完善页面，跳转到个人中心');
      }
    }

    // 默认跳转逻辑
    if (currentRoute === 'pages/login/login') {
      // 从登录页进入，跳转到个人中心页面
      uni.switchTab({
        url: '/pages/mine/mine'
      });
    } else {
      // 其他页面自动登录成功，显示提示并保持当前页面
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000
      });
    }
  } catch (error) {
    console.error('登录导航处理失败:', error);
    
    // 降级处理：默认跳转到首页
    uni.switchTab({
      url: '/pages/index/index'
    });
  }
}

/**
 * 记录登录相关的操作日志
 * @param {Object} logData 日志数据
 */
async function recordLoginOperation(logData) {
  try {
    const { api } = require('@/utils/api.js');
    
    await api.operationLog.recordOperation({
      menu_name: '用户登录',
      button_name: logData.action,
      browser_path: `/${logData.route}`,
      operation_details: JSON.stringify({
        loginType: logData.loginType,
        hasWechatInfo: logData.hasWechatInfo,
        needsUserInfo: logData.needsUserInfo,
        timestamp: new Date().toISOString()
      })
    });
  } catch (error) {
    console.warn('记录操作日志失败:', error);
  }
}

/**
 * 检查登录状态并自动处理
 * @param {Object} options 选项
 * @returns {Promise<Object>} 检查结果
 */
export async function checkAndHandleLoginStatus(options = {}) {
  const defaultOptions = {
    autoLogin: false,
    autoNavigate: false,
    showLoading: false,
    ...options
  };

  try {
    if (defaultOptions.showLoading) {
      uni.showLoading({
        title: '检查登录状态...',
        mask: true
      });
    }

    const result = await autoLoginCheck({
      baseURL: getBaseURL(),
      autoLogin: defaultOptions.autoLogin
    });

    if (result.isLogin) {
      // 已登录
      if (defaultOptions.autoNavigate) {
        if (result.needUserInfo) {
          uni.navigateTo({
            url: '/pages/user-info/user-info'
          });
        } else {
          uni.switchTab({
            url: '/pages/index/index'
          });
        }
      }

      return {
        type: result.needUserInfo ? LOGIN_RESULT_TYPE.NEED_USER_INFO : LOGIN_RESULT_TYPE.SUCCESS,
        success: true,
        isLogin: true,
        needUserInfo: result.needUserInfo,
        userData: result.userData
      };
    } else {
      // 未登录
      return {
        type: LOGIN_RESULT_TYPE.FAILED,
        success: false,
        isLogin: false,
        needLogin: true,
        reason: result.reason
      };
    }
  } catch (error) {
    console.error('检查登录状态失败:', error);
    return {
      type: LOGIN_RESULT_TYPE.FAILED,
      success: false,
      error: error.message
    };
  } finally {
    if (defaultOptions.showLoading) {
      uni.hideLoading();
    }
  }
}

/**
 * 退出登录
 * @param {Object} options 选项
 */
export async function performLogout(options = {}) {
  const defaultOptions = {
    showConfirm: true,
    autoNavigate: true,
    ...options
  };

  try {
    // 显示确认对话框
    if (defaultOptions.showConfirm) {
      const confirmResult = await new Promise((resolve) => {
        uni.showModal({
          title: '确认退出',
          content: '确定要退出登录吗？',
          confirmText: '退出',
          cancelText: '取消',
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        });
      });

      if (!confirmResult) {
        return { success: false, cancelled: true };
      }
    }

    // 清除登录信息
    wechatAuth.clearLoginInfo();
    userStore.clearUserInfo();

    // 显示成功提示
    uni.showToast({
      title: '已退出登录',
      icon: 'success',
      duration: 1500
    });

    // 自动导航到登录页
    if (defaultOptions.autoNavigate) {
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }, 1500);
    }

    return { success: true };
  } catch (error) {
    console.error('退出登录失败:', error);
    uni.showToast({
      title: '退出失败',
      icon: 'none'
    });
    return { success: false, error: error.message };
  }
}

/**
 * 获取错误类型
 * @param {string} errorMessage 错误消息
 * @returns {string} 错误类型
 */
function getErrorType(errorMessage) {
  if (!errorMessage) return 'unknown';
  
  const message = errorMessage.toLowerCase();
  
  if (message.includes('网络') || message.includes('network')) {
    return 'network';
  } else if (message.includes('超时') || message.includes('timeout')) {
    return 'timeout';
  } else if (message.includes('取消') || message.includes('cancel')) {
    return 'cancelled';
  } else if (message.includes('授权') || message.includes('auth')) {
    return 'authorization';
  } else {
    return 'unknown';
  }
}

/**
 * 获取用户友好的错误消息
 * @param {string} errorMessage 原始错误消息
 * @returns {string} 用户友好的错误消息
 */
export function getFriendlyErrorMessage(errorMessage) {
  const errorType = getErrorType(errorMessage);
  
  switch (errorType) {
    case 'network':
      return '网络连接失败，请检查网络设置';
    case 'timeout':
      return '请求超时，请重试';
    case 'cancelled':
      return '用户取消操作';
    case 'authorization':
      return '授权失败，请重新授权';
    default:
      return errorMessage || '操作失败，请重试';
  }
}

/**
 * 显示登录错误提示 - 使用增强的错误处理
 * @param {string|Error} error 错误信息
 * @param {Object} options 选项
 */
export function showLoginError(error, options = {}) {
  const defaultOptions = {
    duration: 2000,
    customMessages: {
      [ERROR_TYPES.NETWORK]: {
        message: '网络连接失败，请检查网络设置'
      },
      [ERROR_TYPES.TIMEOUT]: {
        message: '登录超时，请重试'
      },
      [ERROR_TYPES.AUTHORIZATION]: {
        message: '微信授权失败，请重新登录'
      },
      [ERROR_TYPES.USER_CANCELLED]: {
        message: '用户取消登录'
      }
    },
    ...options
  };

  showError(error, defaultOptions);
}

// 导出状态枚举
export { LOGIN_STATUS, USER_INFO_STATUS };
