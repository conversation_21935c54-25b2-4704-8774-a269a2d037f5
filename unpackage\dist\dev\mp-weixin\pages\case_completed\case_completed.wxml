<view class="work-order-detail-container data-v-1cf6b09e"><view class="work-order-card data-v-1cf6b09e"><view class="work-order-header data-v-1cf6b09e"><view class="work-order-title data-v-1cf6b09e"><text class="data-v-1cf6b09e">调解案件号: </text><text class="work-order-id data-v-1cf6b09e">{{a}}</text></view><view class="work-order-status data-v-1cf6b09e"><text class="{{['status-label', 'data-v-1cf6b09e', c && 'status-pending']}}">{{b}}</text></view></view><view class="work-order-date data-v-1cf6b09e">发起日期: {{d}}</view></view><view class="progress-bar data-v-1cf6b09e"><view class="progress-steps data-v-1cf6b09e"><view class="progress-step active data-v-1cf6b09e"><view class="step-circle data-v-1cf6b09e">1</view><view class="step-line active data-v-1cf6b09e"></view><view class="step-label data-v-1cf6b09e">调解确认</view></view><view class="progress-step active data-v-1cf6b09e"><view class="step-circle data-v-1cf6b09e">2</view><view class="step-line active data-v-1cf6b09e"></view><view class="step-label data-v-1cf6b09e">方案确认</view></view><view class="progress-step active data-v-1cf6b09e"><view class="step-circle data-v-1cf6b09e">3</view><view class="step-line active data-v-1cf6b09e"></view><view class="step-label data-v-1cf6b09e">协议签署</view></view><view class="progress-step active data-v-1cf6b09e"><view class="step-circle data-v-1cf6b09e">4</view><view class="step-label data-v-1cf6b09e">完成</view></view></view></view><view class="info-section data-v-1cf6b09e"><view class="section-title data-v-1cf6b09e">协议公证状态</view><view wx:if="{{e}}" class="notarization-status-card notarization-pending data-v-1cf6b09e" bindtap="{{f}}"><view class="notarization-status-header data-v-1cf6b09e"><view class="notarization-status-icon data-v-1cf6b09e"><view class="fas fa-certificate data-v-1cf6b09e"></view></view><view class="notarization-status-info data-v-1cf6b09e"><view class="notarization-status-title data-v-1cf6b09e">未公证</view><view class="notarization-status-desc data-v-1cf6b09e">协议尚未进行公证，点击进行公证以获得法律强制执行力</view></view><view class="notarization-action-arrow data-v-1cf6b09e"><view class="fas fa-chevron-right data-v-1cf6b09e"></view></view></view><view class="notarization-benefits data-v-1cf6b09e"><view class="notarization-benefit-item data-v-1cf6b09e"><view class="fas fa-shield-alt data-v-1cf6b09e"></view><text class="data-v-1cf6b09e">法律强制执行力</text></view><view class="notarization-benefit-item data-v-1cf6b09e"><view class="fas fa-gavel data-v-1cf6b09e"></view><text class="data-v-1cf6b09e">免费办理</text></view></view></view><view wx:if="{{g}}" class="notarization-status-card notarization-completed notarization-status-card-completed data-v-1cf6b09e" id="notarizationCompletedCard"><view class="notarization-status-header data-v-1cf6b09e"><view class="notarization-status-icon data-v-1cf6b09e"><view class="fas fa-certificate data-v-1cf6b09e"></view></view><view class="notarization-status-info data-v-1cf6b09e"><view class="notarization-status-title data-v-1cf6b09e">已公证</view><view class="notarization-status-desc data-v-1cf6b09e">协议已完成公证，具备法律强制执行力</view></view><view class="notarization-status-badge data-v-1cf6b09e"><view class="fas fa-check-circle data-v-1cf6b09e"></view></view></view><view class="notarization-details data-v-1cf6b09e"><view class="notarization-detail-row data-v-1cf6b09e"><text class="notarization-detail-label data-v-1cf6b09e">公证日期：</text><text class="notarization-detail-value data-v-1cf6b09e">2023-10-15</text></view><view class="notarization-detail-row data-v-1cf6b09e"><text class="notarization-detail-label data-v-1cf6b09e">公证机构：</text><text class="notarization-detail-value data-v-1cf6b09e">广东省佛山市公证处</text></view><view class="notarization-detail-row data-v-1cf6b09e"><text class="notarization-detail-label data-v-1cf6b09e">公证书编号：</text><text class="notarization-detail-value data-v-1cf6b09e">(2023)粤佛证字第12345号</text></view></view></view></view><view class="info-section data-v-1cf6b09e"><view class="section-title data-v-1cf6b09e">调解信息</view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">债权人</text><text class="info-value data-v-1cf6b09e">{{h}}</text></view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">债务类型</text><text class="info-value data-v-1cf6b09e">{{i}}</text></view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">欠款金额</text><text class="info-value data-v-1cf6b09e">¥{{j}}</text></view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">欠款时间</text><text class="info-value data-v-1cf6b09e">{{k}} 至今</text></view></view><view class="info-section data-v-1cf6b09e"><view class="section-title data-v-1cf6b09e">还款方案</view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">方案内容</text><text class="info-value data-v-1cf6b09e">{{l}}</text></view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">还款金额</text><text class="info-value data-v-1cf6b09e">¥{{m}}</text></view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">月还款额</text><text class="info-value data-v-1cf6b09e">¥{{n}}</text></view><view class="info-item data-v-1cf6b09e"><text class="info-label data-v-1cf6b09e">减免金额</text><text class="info-value reduction_amount data-v-1cf6b09e">¥{{o}}</text></view></view><view class="info-section data-v-1cf6b09e"><view class="section-title data-v-1cf6b09e">还款渠道</view><view class="payment-memo-card data-v-1cf6b09e"><view class="payment-memo-header data-v-1cf6b09e"><view class="fas fa-exclamation-triangle payment-memo-icon data-v-1cf6b09e"></view><view class="payment-memo-title data-v-1cf6b09e">重要提醒：还款备注信息</view></view><view class="payment-memo-content data-v-1cf6b09e">{{p}}</view><view class="payment-memo-actions data-v-1cf6b09e"><button wx:if="{{q}}" class="btn btn-sm btn-copy btn-orange data-v-1cf6b09e" bindtap="{{r}}"><view class="fas fa-copy data-v-1cf6b09e"></view>一键复制备注 </button><button wx:else class="btn btn-sm btn-copy btn-orange copied data-v-1cf6b09e"><view class="fas fa-check data-v-1cf6b09e"></view>已复制 </button></view><view class="payment-memo-tip data-v-1cf6b09e"><view class="fas fa-info-circle data-v-1cf6b09e"></view> 还款时请务必填写上述备注信息，以便系统自动识别您的还款记录 </view></view><view class="payment-channel-card data-v-1cf6b09e"><view class="payment-channel-header data-v-1cf6b09e"><view class="payment-channel-icon data-v-1cf6b09e" style="background-color:var(--primary-color)"><view class="fas fa-university data-v-1cf6b09e"></view></view><view class="payment-channel-info data-v-1cf6b09e"><view class="payment-channel-name data-v-1cf6b09e">银行转账</view><view class="payment-channel-desc data-v-1cf6b09e">通过银行转账进行还款</view></view></view><view class="payment-channel-details data-v-1cf6b09e"><view class="payment-detail-row data-v-1cf6b09e"><view class="payment-detail-label data-v-1cf6b09e">收款人姓名</view><view class="payment-detail-value data-v-1cf6b09e">华泰民商事调解中心</view></view><view class="payment-detail-row data-v-1cf6b09e"><view class="payment-detail-label data-v-1cf6b09e">收款账号</view><view class="payment-detail-value data-v-1cf6b09e">6225 8812 3456 7890</view></view><view class="payment-detail-row data-v-1cf6b09e"><view class="payment-detail-label data-v-1cf6b09e">开户银行</view><view class="payment-detail-value data-v-1cf6b09e">中国银行佛山分行</view></view></view></view><view class="payment-channel-card data-v-1cf6b09e"><view class="payment-channel-header data-v-1cf6b09e"><view class="payment-channel-icon data-v-1cf6b09e" style="background-color:#09bb07"><view class="fab fa-weixin data-v-1cf6b09e"></view></view><view class="payment-channel-info data-v-1cf6b09e"><view class="payment-channel-name data-v-1cf6b09e">微信支付</view><view class="payment-channel-desc data-v-1cf6b09e">扫码使用微信支付进行还款</view></view></view><view class="payment-channel-qrcode-container data-v-1cf6b09e"><view class="payment-channel-qrcode data-v-1cf6b09e"> 收款二维码 </view></view></view><view class="payment-channel-card data-v-1cf6b09e"><view class="payment-channel-header data-v-1cf6b09e"><view class="payment-channel-icon data-v-1cf6b09e" style="background-color:#00a0e9"><view class="fab fa-alipay data-v-1cf6b09e"></view></view><view class="payment-channel-info data-v-1cf6b09e"><view class="payment-channel-name data-v-1cf6b09e">支付宝</view><view class="payment-channel-desc data-v-1cf6b09e">扫码使用支付宝进行还款</view></view></view><view class="payment-channel-qrcode-container data-v-1cf6b09e"><view class="payment-channel-qrcode data-v-1cf6b09e"> 收款二维码 </view></view></view></view><view class="action-buttons data-v-1cf6b09e"><button class="confirm-button data-v-1cf6b09e" bindtap="{{s}}"><view class="fas fa-file-contract data-v-1cf6b09e"></view>查看协议</button></view></view>