"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "work_order_detail",
  setup(__props) {
    const orderId = common_vendor.ref("");
    const orderStatus = common_vendor.ref("");
    const workOrderData = common_vendor.ref({
      id: "MED20230001",
      status: "待确认",
      createDate: "2023-11-01",
      creditor: "某银行信用卡中心",
      debtType: "信用卡欠款",
      amount: "50,000.00",
      debtDate: "2022-05-01",
      files: [
        {
          id: 1,
          name: "债权转让协议书.pdf"
        },
        {
          id: 2,
          name: "借款合同扫描件.jpg"
        },
        {
          id: 3,
          name: "身份证明材料.png"
        }
      ]
    });
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:124", "页面参数:", options);
      if (options.id) {
        orderId.value = options.id;
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:129", "接收到调解ID:", orderId.value);
      }
      if (options.status) {
        orderStatus.value = options.status;
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:135", "接收到调解状态:", orderStatus.value);
        if (orderStatus.value === "已关闭") {
          workOrderData.value.status = orderStatus.value;
          common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:142", "检测到已关闭状态，已更新workOrderData状态为:", orderStatus.value);
        }
      }
      if (orderId.value) {
        fetchWorkOrderDetail(orderId.value);
      } else {
        fetchWorkOrderDetail();
      }
    });
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:157", "工单详情页面组件已挂载");
    });
    const fetchWorkOrderDetail = (id) => {
      if (id) {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        utils_api.api.workOrder.getDetail(id).then((res) => {
          common_vendor.index.hideLoading();
          if (res.code === 0) {
            workOrderData.value = res.data;
          } else {
            common_vendor.index.showToast({
              title: res.message || "获取调解确认失败",
              icon: "none"
            });
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:183", "获取调解确认失败", err);
          common_vendor.index.showToast({
            title: "获取调解确认失败",
            icon: "none"
          });
        });
      } else {
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:192", "调解确认数据已加载（模拟）");
        }, 500);
      }
    };
    const handleAccept = () => {
      common_vendor.index.showModal({
        title: "确认接受",
        content: "您确定要接受此调解吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            common_vendor.index.navigateTo({
              url: `/pages/solution_confirm/solution_confirm?orderId=${workOrderData.value.id}`,
              success: () => {
                common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:214", "跳转到调解方案确认页面");
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:217", "跳转失败", err);
                common_vendor.index.showToast({
                  title: "跳转失败",
                  icon: "none"
                });
              }
            });
          }
        }
      });
    };
    const handleReject = () => {
      common_vendor.index.navigateTo({
        url: `/pages/mediation_query/mediation_query`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(workOrderData.value.id || "MED20230001"),
        b: common_vendor.t(workOrderData.value.status || "待确认"),
        c: workOrderData.value.status === "待确认" ? 1 : "",
        d: common_vendor.t(workOrderData.value.createDate || "2023-11-01"),
        e: workOrderData.value.status === "已关闭"
      }, workOrderData.value.status === "已关闭" ? {
        f: common_vendor.t(workOrderData.value.closingDate || "2023-12-01")
      } : {}, {
        g: common_vendor.t(workOrderData.value.creditor || "某银行信用卡中心"),
        h: common_vendor.t(workOrderData.value.debtType || "信用卡欠款"),
        i: common_vendor.t(workOrderData.value.amount || "50,000.00"),
        j: common_vendor.t(workOrderData.value.debtDate || "2022-05-01"),
        k: common_vendor.f(workOrderData.value.files, (file, k0, i0) => {
          return {
            a: common_vendor.n(file.name.split(".").pop() === "pdf" ? "fa-file-pdf" : "fa-file-image"),
            b: file.name.split(".").pop() === "pdf" ? "#ff4d4f" : "#52c41a",
            c: common_vendor.t(file.name),
            d: file.id
          };
        }),
        l: workOrderData.value.status === "已关闭"
      }, workOrderData.value.status === "已关闭" ? {
        m: common_vendor.t(workOrderData.value.closingReason || "调解案件已超过规定期限。")
      } : {
        n: common_vendor.o(handleAccept),
        o: common_vendor.o(handleReject)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/work_order_detail/work_order_detail.js.map
