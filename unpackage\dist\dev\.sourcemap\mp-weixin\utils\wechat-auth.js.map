{"version": 3, "file": "wechat-auth.js", "sources": ["utils/wechat-auth.js"], "sourcesContent": ["// 微信小程序认证工具 - 2024年最新版本\nimport { getWechatAppId, isDebug } from '@/config/env.js';\nimport { authUtils } from '@/server/require.js';\nimport { showWechatError } from '@/utils/wechat-error-handler.js';\nimport { api } from '@/utils/api.js';\n\n/**\n * 微信登录状态枚举\n */\nexport const LOGIN_STATUS = {\n  NOT_LOGIN: 'not_login',      // 未登录\n  LOGGING: 'logging',          // 登录中\n  LOGIN_SUCCESS: 'login_success', // 登录成功\n  LOGIN_FAILED: 'login_failed'    // 登录失败\n};\n\n/**\n * 用户信息获取状态枚举\n */\nexport const USER_INFO_STATUS = {\n  NOT_AUTHORIZED: 'not_authorized',    // 未授权\n  AUTHORIZING: 'authorizing',          // 授权中\n  AUTHORIZED: 'authorized',            // 已授权\n  AUTHORIZATION_FAILED: 'auth_failed'  // 授权失败\n};\n\n/**\n * 微信登录工具类 - 支持最新的登录流程和用户信息获取\n */\nclass WechatAuth {\n  constructor() {\n    this.loginStatus = LOGIN_STATUS.NOT_LOGIN;\n    this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;\n    this.userInfo = null;\n    this.wechatUserInfo = null; // 通过wx.getUserProfile获取的用户信息\n    this.sessionKey = null;\n    this.openid = null;\n    this.unionid = null;\n    this.loginRetryCount = 0;\n    this.maxRetryCount = 3;\n    this.sessionCheckTimer = null;\n\n    // 启动会话检查定时器\n    this.startSessionCheck();\n  }\n\n  /**\n   * 启动会话检查定时器 - 每30分钟检查一次登录状态\n   */\n  startSessionCheck() {\n    if (this.sessionCheckTimer) {\n      clearInterval(this.sessionCheckTimer);\n    }\n\n    // 每30分钟检查一次会话状态\n    this.sessionCheckTimer = setInterval(() => {\n      this.checkLoginStatus().catch(error => {\n        if (isDebug()) {\n          console.warn('定时会话检查失败:', error);\n        }\n      });\n    }, 30 * 60 * 1000);\n  }\n\n  /**\n   * 停止会话检查定时器\n   */\n  stopSessionCheck() {\n    if (this.sessionCheckTimer) {\n      clearInterval(this.sessionCheckTimer);\n      this.sessionCheckTimer = null;\n    }\n  }\n\n  /**\n   * 检查微信登录状态 - 增强版本，支持自动续期\n   */\n  checkLoginStatus() {\n    return new Promise((resolve) => {\n      wx.checkSession({\n        success: () => {\n          // session_key 未过期，并且在本生命周期一直有效\n          const access_token = uni.getStorageSync('access_token');\n          const tokenExpireTime = uni.getStorageSync('token_expire_time');\n          const currentTime = Date.now();\n\n          if (access_token && (!tokenExpireTime || currentTime < tokenExpireTime)) {\n            this.loginStatus = LOGIN_STATUS.LOGIN_SUCCESS;\n            resolve({\n              isLogin: true,\n              needReLogin: false,\n              tokenValid: true\n            });\n          } else {\n            // token过期，需要重新登录\n            this.loginStatus = LOGIN_STATUS.NOT_LOGIN;\n            resolve({\n              isLogin: false,\n              needReLogin: true,\n              tokenValid: false,\n              reason: 'token_expired'\n            });\n          }\n        },\n        fail: (error) => {\n          // session_key 已经失效，需要重新执行登录流程\n          this.loginStatus = LOGIN_STATUS.NOT_LOGIN;\n          if (isDebug()) {\n            console.log('微信会话已失效:', error);\n          }\n          resolve({\n            isLogin: false,\n            needReLogin: true,\n            tokenValid: false,\n            reason: 'session_expired'\n          });\n        }\n      });\n    });\n  }\n\n  /**\n   * 微信登录 - 增强版本，支持完整的用户授权流程\n   * @param {Object} options 登录选项\n   * @returns {Promise}\n   */\n  login(options = {}) {\n    return new Promise((resolve, reject) => {\n      if (this.loginStatus === LOGIN_STATUS.LOGGING) {\n        reject(new Error('正在登录中，请勿重复操作'));\n        return;\n      }\n\n      // 重置重试计数\n      this.loginRetryCount = 0;\n      this._performLogin(options, resolve, reject);\n    });\n  }\n\n  /**\n   * 执行登录操作（内部方法，支持重试和完整流程）\n   */\n  _performLogin(options, resolve, reject) {\n    this.loginStatus = LOGIN_STATUS.LOGGING;\n\n    // 设置超时时间，默认15秒（增加时间以支持用户授权）\n    const timeout = options.timeout || 15000;\n    const timeoutTimer = setTimeout(() => {\n      this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;\n      reject(new Error('登录超时，请检查网络连接'));\n    }, timeout);\n\n    // 优化后的登录流程：先获取用户信息授权，再进行登录\n    this._getUserProfileWithFallback(options)\n      .then((userProfileResult) => {\n        if (isDebug()) {\n          console.log('用户信息获取完成:', userProfileResult);\n        }\n\n        // 获取微信登录code\n        return this._getWechatLoginCode();\n      })\n      .then((code) => {\n        clearTimeout(timeoutTimer);\n        \n        if (isDebug()) {\n          console.log('微信登录获取code成功:', code);\n        }\n\n        // 调用后端接口完成登录\n        return this.exchangeSessionKey(code, options);\n      })\n      .then(resolve)\n      .catch((error) => {\n        clearTimeout(timeoutTimer);\n        \n        // 如果是网络错误且未达到最大重试次数，则重试\n        if (this.loginRetryCount < this.maxRetryCount &&\n            (error.message.includes('网络') || error.message.includes('超时'))) {\n          this.loginRetryCount++;\n          if (isDebug()) {\n            console.log(`登录失败，正在重试 (${this.loginRetryCount}/${this.maxRetryCount}):`, error.message);\n          }\n          setTimeout(() => {\n            this._performLogin(options, resolve, reject);\n          }, 1000 * this.loginRetryCount); // 递增延迟重试\n        } else {\n          this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;\n          reject(error);\n        }\n      });\n  }\n\n  /**\n   * 获取用户信息授权（支持降级处理）\n   * @param {Object} options 选项\n   * @returns {Promise}\n   */\n  _getUserProfileWithFallback(options = {}) {\n    return new Promise((resolve) => {\n      // 检查是否支持getUserProfile API\n      if (typeof wx.getUserProfile === 'function') {\n        // 显示授权说明\n        if (options.showAuthTip !== false) {\n          uni.showModal({\n            title: '用户信息授权',\n            content: '为了提供更好的服务体验，需要获取您的微信头像和昵称信息，该信息仅用于个人账户展示',\n            confirmText: '同意授权',\n            cancelText: '暂不授权',\n            success: (modalRes) => {\n              if (modalRes.confirm) {\n                this._callGetUserProfile(resolve);\n              } else {\n                // 用户拒绝授权，但继续登录流程\n                if (isDebug()) {\n                  console.log('用户拒绝授权，继续登录流程');\n                }\n                this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;\n                resolve({ \n                  success: false, \n                  reason: 'user_denied',\n                  message: '用户拒绝授权，已跳过用户信息获取',\n                  shouldContinueLogin: true // 标记应该继续登录流程\n                });\n              }\n            },\n            fail: () => {\n              // Modal显示失败，继续登录流程（不再尝试获取用户信息）\n              if (isDebug()) {\n                console.log('授权弹窗显示失败，继续登录流程');\n              }\n              this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;\n              resolve({ \n                success: false, \n                reason: 'modal_failed',\n                message: '授权弹窗显示失败，已跳过用户信息获取',\n                shouldContinueLogin: true\n              });\n            }\n          });\n        } else {\n          // 直接调用getUserProfile\n          this._callGetUserProfile(resolve);\n        }\n      } else {\n        // API不可用，跳过用户信息获取，继续登录流程\n        if (isDebug()) {\n          console.log('getUserProfile API不可用，跳过用户信息获取');\n        }\n        this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;\n        resolve({ \n          success: false, \n          reason: 'api_unavailable',\n          message: 'getUserProfile API不可用',\n          shouldContinueLogin: true\n        });\n      }\n    });\n  }\n\n  /**\n   * 调用getUserProfile API\n   * @param {Function} resolve Promise resolve函数\n   */\n  _callGetUserProfile(resolve) {\n    this.userInfoStatus = USER_INFO_STATUS.AUTHORIZING;\n    \n    wx.getUserProfile({\n      desc: '用于完善会员资料', // 必填，说明获取用户信息的用途\n      success: (res) => {\n        if (isDebug()) {\n          console.log('获取用户信息成功:', res.userInfo);\n        }\n\n        this.wechatUserInfo = res.userInfo;\n        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;\n        \n        // 保存用户信息到本地\n        uni.setStorageSync('wechat_userInfo', res.userInfo);\n\n        resolve({\n          success: true,\n          userInfo: res.userInfo,\n          message: '用户信息获取成功'\n        });\n      },\n      fail: (error) => {\n        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;\n        \n        if (isDebug()) {\n          console.error('getUserProfile失败:', error);\n        }\n\n        // 用户拒绝或其他错误，继续登录流程\n        resolve({\n          success: false,\n          reason: 'get_profile_failed',\n          error: error.errMsg,\n          message: '获取用户信息失败，但继续登录流程',\n          shouldContinueLogin: true // 继续登录流程\n        });\n      }\n    });\n  }\n\n  /**\n   * 获取微信登录code\n   * @returns {Promise<string>}\n   */\n  _getWechatLoginCode() {\n    return new Promise((resolve, reject) => {\n      wx.login({\n        success: (loginRes) => {\n          if (loginRes.code) {\n            resolve(loginRes.code);\n          } else {\n            reject(new Error('微信登录失败：' + loginRes.errMsg));\n          }\n        },\n        fail: (error) => {\n          reject(new Error('微信登录失败：' + error.errMsg));\n        }\n      });\n    });\n  }\n\n  /**\n   * 向后端换取session_key - 增强版本，支持用户信息传递\n   * @param {string} code 微信登录code\n   * @param {Object} options 选项\n   */\n  exchangeSessionKey(code, options = {}) {\n    return new Promise((resolve, reject) => {\n      const requestTimeout = options.requestTimeout || 8000;\n\n      // 准备请求数据\n      let requestData = {\n        js_code: code\n      };\n\n      // 如果获取到了用户信息，则一并传递（使用用户修改后的格式）\n      if (this.wechatUserInfo) {\n        requestData.nickname = this.wechatUserInfo.nickName;\n        requestData.avatar_url = this.wechatUserInfo.avatarUrl;\n        if (isDebug()) {\n          console.log(requestData,'包含用户信息的登录请求:', {\n            js_code: code,\n            hasUserInfo: true,\n            userInfo: this.wechatUserInfo\n          });\n        }\n      } else {\n        if (isDebug()) {\n          console.log('仅包含code的登录请求:', { js_code: code });\n        }\n      }\n\n      // 调用后端API换取session_key - 使用统一的API管理\n      api.wechat.login(requestData)\n        .then((responseData) => {\n          if (isDebug()) {\n            console.log('后端登录响应:', responseData);\n          }\n\n          try {\n            // API统一管理已处理状态码和基础错误，这里直接处理业务数据\n            const { access_token, token_type, userInfo, openid, unionid, sessionKey, expiresIn, refresh_token } = responseData;\n\n            // 重要：无论是否获取到微信用户信息，都要保存登录凭证\n            // 使用统一的token管理工具保存登录信息\n            if (access_token) {\n              authUtils.setTokenInfo({\n                access_token,\n                token_type,\n                expires_in: expiresIn\n              });\n            }\n\n            // 保存refresh_token（如果后端返回了的话）\n            if (refresh_token) {\n              authUtils.setRefreshToken(refresh_token);\n            }\n\n            // 保存其他登录信息（包括openid）\n            this.saveLoginInfo({\n              access_token,\n              token_type,\n              userInfo,\n              openid,\n              unionid,\n              sessionKey,\n              expiresIn\n            });\n\n            this.loginStatus = LOGIN_STATUS.LOGIN_SUCCESS;\n\n            // 显示适当的成功提示\n            if (this.wechatUserInfo) {\n              uni.showToast({\n                title: '登录成功',\n                icon: 'success',\n                duration: 2000\n              });\n            } else {\n              uni.showToast({\n                title: '登录成功，建议完善个人信息',\n                icon: 'none',\n                duration: 3000\n              });\n            }\n\n            resolve({\n              success: true,\n              access_token,\n              token_type,\n              userInfo,\n              openid,\n              unionid,\n              expiresIn,\n              refresh_token,\n              wechatUserInfo: this.wechatUserInfo // 可能为null，但这是正常的\n            });\n          } catch (error) {\n            // 处理数据解析错误\n            if (isDebug()) {\n              console.error('登录数据解析错误:', error);\n            }\n            this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;\n            reject(new Error('登录数据处理失败'));\n          }\n        })\n        .catch((error) => {\n          this.loginStatus = LOGIN_STATUS.LOGIN_FAILED;\n\n          if (isDebug()) {\n            console.error('微信登录API调用失败:', error);\n          }\n\n          // API统一管理已处理网络错误，这里直接传递错误信息\n          reject(error);\n        });\n    });\n  }\n\n  /**\n   * 获取用户信息（保留原有方法，增加更好的错误提示）\n   */\n  getUserProfile() {\n    return new Promise((resolve, reject) => {\n      // 检查是否支持getUserProfile\n      if (typeof wx.getUserProfile === 'function') {\n        wx.getUserProfile({\n          desc: '用于完善用户资料',\n          success: (res) => {\n            if (isDebug()) {\n              console.log('获取用户信息成功:', res.userInfo);\n            }\n\n            this.wechatUserInfo = res.userInfo;\n            this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;\n            // 保存用户信息到本地\n            uni.setStorageSync('wechat_userInfo', res.userInfo);\n\n            resolve(res.userInfo);\n          },\n          fail: (error) => {\n            this.userInfoStatus = USER_INFO_STATUS.AUTHORIZATION_FAILED;\n            \n            // 根据错误类型提供更友好的提示\n            let errorMessage = '获取用户信息失败';\n            if (error.errMsg && error.errMsg.includes('deny')) {\n              errorMessage = '用户拒绝授权获取信息';\n            } else if (error.errMsg && error.errMsg.includes('auth')) {\n              errorMessage = '用户信息授权失败';\n            }\n            \n            if (isDebug()) {\n              console.error('getUserProfile失败:', error);\n            }\n            reject(new Error(errorMessage));\n          }\n        });\n      } else {\n        // API不可用，提示使用新的头像昵称填写能力\n        reject(new Error('当前微信版本不支持getUserProfile，请升级微信版本或使用其他登录方式'));\n      }\n    });\n  }\n\n  /**\n   * 处理头像选择 - 新的头像昵称填写能力\n   * @param {Object} event 头像选择事件对象\n   * @returns {Promise}\n   */\n  handleChooseAvatar(event) {\n    return new Promise((resolve, reject) => {\n      try {\n        const { avatarUrl } = event.detail;\n        if (!avatarUrl) {\n          reject(new Error('未获取到头像信息'));\n          return;\n        }\n\n        if (isDebug()) {\n          console.log('用户选择头像:', avatarUrl);\n        }\n\n        // 更新本地用户信息\n        const currentUserInfo = this.wechatUserInfo || {};\n        const updatedUserInfo = {\n          ...currentUserInfo,\n          avatarUrl: avatarUrl,\n          updateTime: Date.now()\n        };\n\n        this.wechatUserInfo = updatedUserInfo;\n        uni.setStorageSync('wechat_userInfo', updatedUserInfo);\n\n        resolve({\n          success: true,\n          avatarUrl: avatarUrl,\n          userInfo: updatedUserInfo\n        });\n      } catch (error) {\n        if (isDebug()) {\n          console.error('处理头像选择失败:', error);\n        }\n        reject(new Error('处理头像选择失败：' + error.message));\n      }\n    });\n  }\n\n  /**\n   * 处理昵称输入 - 新的头像昵称填写能力\n   * @param {string} nickname 用户输入的昵称\n   * @returns {Promise}\n   */\n  handleNicknameInput(nickname) {\n    return new Promise((resolve, reject) => {\n      try {\n        if (!nickname || nickname.trim().length === 0) {\n          reject(new Error('昵称不能为空'));\n          return;\n        }\n\n        // 昵称长度限制\n        if (nickname.length > 20) {\n          reject(new Error('昵称长度不能超过20个字符'));\n          return;\n        }\n\n        if (isDebug()) {\n          console.log('用户输入昵称:', nickname);\n        }\n\n        // 更新本地用户信息\n        const currentUserInfo = this.wechatUserInfo || {};\n        const updatedUserInfo = {\n          ...currentUserInfo,\n          nickName: nickname.trim(),\n          updateTime: Date.now()\n        };\n\n        this.wechatUserInfo = updatedUserInfo;\n        this.userInfoStatus = USER_INFO_STATUS.AUTHORIZED;\n        uni.setStorageSync('wechat_userInfo', updatedUserInfo);\n\n        resolve({\n          success: true,\n          nickName: nickname.trim(),\n          userInfo: updatedUserInfo\n        });\n      } catch (error) {\n        if (isDebug()) {\n          console.error('处理昵称输入失败:', error);\n        }\n        reject(new Error('处理昵称输入失败：' + error.message));\n      }\n    });\n  }\n\n  /**\n   * 保存登录信息到本地存储 - 增强版本，支持过期时间管理\n   */\n  saveLoginInfo({ access_token, token_type, userInfo, openid, unionid, sessionKey, expiresIn }) {\n    try {\n      const currentTime = Date.now();\n\n      if (access_token) {\n        uni.setStorageSync('access_token', access_token);\n\n        // 保存token类型（默认为Bearer）\n        const tokenType = token_type || 'Bearer';\n        uni.setStorageSync('token_type', tokenType);\n\n        // 计算token过期时间（默认7天）\n        const expireTime = expiresIn ?\n          currentTime + (expiresIn * 1000) :\n          currentTime + (7 * 24 * 60 * 60 * 1000);\n        uni.setStorageSync('token_expire_time', expireTime);\n      }\n\n      if (userInfo) {\n        uni.setStorageSync('userInfo', userInfo);\n        this.userInfo = userInfo;\n      }\n\n      if (openid) {\n        uni.setStorageSync('openid', openid);\n        this.openid = openid;\n      }\n\n      if (unionid) {\n        uni.setStorageSync('unionid', unionid);\n        this.unionid = unionid;\n      }\n\n      if (sessionKey) {\n        this.sessionKey = sessionKey;\n        // 不保存sessionKey到本地存储，仅保存在内存中\n      }\n\n      // 保存登录时间\n      uni.setStorageSync('login_time', currentTime);\n\n      if (isDebug()) {\n        console.log('登录信息已保存到本地存储', {\n          hasToken: !!access_token,\n          hasUserInfo: !!userInfo,\n          hasOpenid: !!openid,\n          hasUnionid: !!unionid,\n          expiresIn: expiresIn\n        });\n      }\n    } catch (error) {\n      console.error('保存登录信息失败:', error);\n      throw new Error('保存登录信息失败');\n    }\n  }\n\n  /**\n   * 清除登录信息 - 增强版本\n   */\n  clearLoginInfo() {\n    try {\n      // 停止会话检查定时器\n      this.stopSessionCheck();\n\n      // 清除所有存储的登录相关信息\n      const keysToRemove = [\n        'access_token',\n        'token_type',\n        'token_expire_time',\n        'userInfo',\n        'openid',\n        'unionid',\n        'wechat_userInfo',\n        'login_time'\n      ];\n\n      keysToRemove.forEach(key => {\n        try {\n          uni.removeStorageSync(key);\n        } catch (e) {\n          if (isDebug()) {\n            console.warn(`清除存储项 ${key} 失败:`, e);\n          }\n        }\n      });\n\n      // 重置内存中的状态\n      this.loginStatus = LOGIN_STATUS.NOT_LOGIN;\n      this.userInfoStatus = USER_INFO_STATUS.NOT_AUTHORIZED;\n      this.userInfo = null;\n      this.wechatUserInfo = null;\n      this.sessionKey = null;\n      this.openid = null;\n      this.unionid = null;\n      this.loginRetryCount = 0;\n\n      if (isDebug()) {\n        console.log('登录信息已完全清除');\n      }\n    } catch (error) {\n      console.error('清除登录信息失败:', error);\n      throw new Error('清除登录信息失败');\n    }\n  }\n\n  /**\n   * 获取当前登录状态\n   */\n  getLoginStatus() {\n    return {\n      loginStatus: this.loginStatus,\n      userInfoStatus: this.userInfoStatus\n    };\n  }\n\n  /**\n   * 获取存储的用户信息 - 增强版本，包含完整性检查\n   */\n  getStoredUserInfo() {\n    try {\n      const userInfo = uni.getStorageSync('userInfo');\n      const wechatUserInfo = uni.getStorageSync('wechat_userInfo');\n      const loginTime = uni.getStorageSync('login_time');\n      const tokenExpireTime = uni.getStorageSync('token_expire_time');\n\n      return {\n        userInfo,\n        wechatUserInfo,\n        loginTime,\n        tokenExpireTime,\n        isTokenValid: tokenExpireTime ? Date.now() < tokenExpireTime : false,\n        hasCompleteUserInfo: !!(wechatUserInfo && wechatUserInfo.nickName && wechatUserInfo.avatarUrl)\n      };\n    } catch (error) {\n      console.error('获取存储的用户信息失败:', error);\n      return {\n        userInfo: null,\n        wechatUserInfo: null,\n        loginTime: null,\n        tokenExpireTime: null,\n        isTokenValid: false,\n        hasCompleteUserInfo: false\n      };\n    }\n  }\n\n  /**\n   * 获取完整的用户数据对象\n   */\n  getCompleteUserData() {\n    const storedInfo = this.getStoredUserInfo();\n    const openid = this.getOpenid();\n    const unionid = this.getUnionid();\n\n    return {\n      // 基础登录信息\n      openid,\n      unionid,\n      loginTime: storedInfo.loginTime,\n      tokenExpireTime: storedInfo.tokenExpireTime,\n      isTokenValid: storedInfo.isTokenValid,\n\n      // 后端返回的用户信息\n      userInfo: storedInfo.userInfo,\n\n      // 微信用户信息（通过头像昵称填写能力获取）\n      wechatUserInfo: storedInfo.wechatUserInfo,\n\n      // 状态信息\n      loginStatus: this.loginStatus,\n      userInfoStatus: this.userInfoStatus,\n      hasCompleteUserInfo: storedInfo.hasCompleteUserInfo\n    };\n  }\n\n  /**\n   * 获取openid\n   */\n  getOpenid() {\n    return this.openid || uni.getStorageSync('openid');\n  }\n\n  /**\n   * 获取unionid\n   */\n  getUnionid() {\n    return this.unionid || uni.getStorageSync('unionid');\n  }\n\n  /**\n   * 检查是否需要获取用户信息\n   */\n  needUserInfo() {\n    const { hasCompleteUserInfo } = this.getStoredUserInfo();\n    return !hasCompleteUserInfo;\n  }\n\n  /**\n   * 销毁实例（清理资源）\n   */\n  destroy() {\n    this.stopSessionCheck();\n    this.clearLoginInfo();\n  }\n}\n\n// 创建单例实例\nconst wechatAuth = new WechatAuth();\n\n// 导出实例和工具函数\nexport default wechatAuth;\n\n/**\n * 快捷登录函数 - 支持完整的登录流程\n */\nexport function quickLogin(options = {}) {\n  return wechatAuth.login(options);\n}\n\n/**\n * 检查登录状态\n */\nexport function checkLogin() {\n  return wechatAuth.checkLoginStatus();\n}\n\n/**\n * 退出登录 - 完整清理\n */\nexport function logout() {\n  wechatAuth.clearLoginInfo();\n  return Promise.resolve({ success: true });\n}\n\n/**\n * 获取用户信息\n */\nexport function getUserInfo() {\n  return wechatAuth.getStoredUserInfo();\n}\n\n/**\n * 获取完整用户数据\n */\nexport function getCompleteUserData() {\n  return wechatAuth.getCompleteUserData();\n}\n\n/**\n * 处理头像选择\n */\nexport function handleChooseAvatar(event) {\n  return wechatAuth.handleChooseAvatar(event);\n}\n\n/**\n * 处理昵称输入\n */\nexport function handleNicknameInput(nickname) {\n  return wechatAuth.handleNicknameInput(nickname);\n}\n\n/**\n * 检查是否需要获取用户信息\n */\nexport function needUserInfo() {\n  return wechatAuth.needUserInfo();\n}\n\n/**\n * 获取用户信息状态\n */\nexport function getUserInfoStatus() {\n  return wechatAuth.userInfoStatus;\n}\n\n/**\n * 自动登录检查 - 检查登录状态并在需要时自动登录\n */\nexport function autoLoginCheck(options = {}) {\n  return new Promise(async (resolve, reject) => {\n    try {\n      const loginStatus = await wechatAuth.checkLoginStatus();\n\n      if (loginStatus.isLogin) {\n        // 已登录，返回用户数据\n        resolve({\n          isLogin: true,\n          userData: wechatAuth.getCompleteUserData(),\n          needUserInfo: wechatAuth.needUserInfo()\n        });\n      } else if (options.autoLogin !== false) {\n        // 自动重新登录\n        try {\n          const loginResult = await wechatAuth.login(options);\n          resolve({\n            isLogin: true,\n            userData: wechatAuth.getCompleteUserData(),\n            needUserInfo: wechatAuth.needUserInfo(),\n            autoLoginSuccess: true\n          });\n        } catch (loginError) {\n          resolve({\n            isLogin: false,\n            needLogin: true,\n            autoLoginFailed: true,\n            error: loginError.message\n          });\n        }\n      } else {\n        // 不自动登录\n        resolve({\n          isLogin: false,\n          needLogin: true,\n          reason: loginStatus.reason\n        });\n      }\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n// 状态枚举已在文件开头导出，这里不需要重复导出\n"], "names": ["isDebug", "uni", "wx", "api", "authUtils"], "mappings": ";;;;;AASO,MAAM,eAAe;AAAA,EAC1B,WAAW;AAAA;AAAA,EACX,SAAS;AAAA;AAAA,EACT,eAAe;AAAA;AAAA,EACf,cAAc;AAAA;AAChB;AAKO,MAAM,mBAAmB;AAAA,EAC9B,gBAAgB;AAAA;AAAA,EAChB,aAAa;AAAA;AAAA,EACb,YAAY;AAAA;AAAA,EACZ,sBAAsB;AAAA;AACxB;AAKA,MAAM,WAAW;AAAA,EACf,cAAc;AACZ,SAAK,cAAc,aAAa;AAChC,SAAK,iBAAiB,iBAAiB;AACvC,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AAGzB,SAAK,kBAAiB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AAClB,QAAI,KAAK,mBAAmB;AAC1B,oBAAc,KAAK,iBAAiB;AAAA,IACrC;AAGD,SAAK,oBAAoB,YAAY,MAAM;AACzC,WAAK,iBAAgB,EAAG,MAAM,WAAS;AACrC,YAAIA,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,QAAA,8BAAa,aAAa,KAAK;AAAA,QAChC;AAAA,MACT,CAAO;AAAA,IACP,GAAO,KAAK,KAAK,GAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AACjB,QAAI,KAAK,mBAAmB;AAC1B,oBAAc,KAAK,iBAAiB;AACpC,WAAK,oBAAoB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AACjB,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9BC,oBAAAA,KAAG,aAAa;AAAA,QACd,SAAS,MAAM;AAEb,gBAAM,eAAeD,cAAAA,MAAI,eAAe,cAAc;AACtD,gBAAM,kBAAkBA,cAAAA,MAAI,eAAe,mBAAmB;AAC9D,gBAAM,cAAc,KAAK;AAEzB,cAAI,iBAAiB,CAAC,mBAAmB,cAAc,kBAAkB;AACvE,iBAAK,cAAc,aAAa;AAChC,oBAAQ;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,cACb,YAAY;AAAA,YAC1B,CAAa;AAAA,UACb,OAAiB;AAEL,iBAAK,cAAc,aAAa;AAChC,oBAAQ;AAAA,cACN,SAAS;AAAA,cACT,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,QAAQ;AAAA,YACtB,CAAa;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,CAAC,UAAU;AAEf,eAAK,cAAc,aAAa;AAChC,cAAID,WAAO,QAAA,GAAI;AACbC,0BAAA,MAAA,MAAA,OAAA,+BAAY,YAAY,KAAK;AAAA,UAC9B;AACD,kBAAQ;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,QAAQ;AAAA,UACpB,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,UAAU,IAAI;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,KAAK,gBAAgB,aAAa,SAAS;AAC7C,eAAO,IAAI,MAAM,cAAc,CAAC;AAChC;AAAA,MACD;AAGD,WAAK,kBAAkB;AACvB,WAAK,cAAc,SAAS,SAAS,MAAM;AAAA,IACjD,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,SAAS,SAAS,QAAQ;AACtC,SAAK,cAAc,aAAa;AAGhC,UAAM,UAAU,QAAQ,WAAW;AACnC,UAAM,eAAe,WAAW,MAAM;AACpC,WAAK,cAAc,aAAa;AAChC,aAAO,IAAI,MAAM,cAAc,CAAC;AAAA,IACjC,GAAE,OAAO;AAGV,SAAK,4BAA4B,OAAO,EACrC,KAAK,CAAC,sBAAsB;AAC3B,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAY,MAAA,MAAA,OAAA,+BAAA,aAAa,iBAAiB;AAAA,MAC3C;AAGD,aAAO,KAAK;IACpB,CAAO,EACA,KAAK,CAAC,SAAS;AACd,mBAAa,YAAY;AAEzB,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAY,MAAA,MAAA,OAAA,+BAAA,iBAAiB,IAAI;AAAA,MAClC;AAGD,aAAO,KAAK,mBAAmB,MAAM,OAAO;AAAA,IACpD,CAAO,EACA,KAAK,OAAO,EACZ,MAAM,CAAC,UAAU;AAChB,mBAAa,YAAY;AAGzB,UAAI,KAAK,kBAAkB,KAAK,kBAC3B,MAAM,QAAQ,SAAS,IAAI,KAAK,MAAM,QAAQ,SAAS,IAAI,IAAI;AAClE,aAAK;AACL,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,OAAA,+BAAY,cAAc,KAAK,eAAe,IAAI,KAAK,aAAa,MAAM,MAAM,OAAO;AAAA,QACxF;AACD,mBAAW,MAAM;AACf,eAAK,cAAc,SAAS,SAAS,MAAM;AAAA,QACvD,GAAa,MAAO,KAAK,eAAe;AAAA,MACxC,OAAe;AACL,aAAK,cAAc,aAAa;AAChC,eAAO,KAAK;AAAA,MACb;AAAA,IACT,CAAO;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,4BAA4B,UAAU,IAAI;AACxC,WAAO,IAAI,QAAQ,CAAC,YAAY;AAE9B,UAAI,OAAOC,cAAAA,KAAG,mBAAmB,YAAY;AAE3C,YAAI,QAAQ,gBAAgB,OAAO;AACjCD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,SAAS,CAAC,aAAa;AACrB,kBAAI,SAAS,SAAS;AACpB,qBAAK,oBAAoB,OAAO;AAAA,cAChD,OAAqB;AAEL,oBAAID,WAAO,QAAA,GAAI;AACbC,gCAAAA,MAAY,MAAA,OAAA,+BAAA,eAAe;AAAA,gBAC5B;AACD,qBAAK,iBAAiB,iBAAiB;AACvC,wBAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,SAAS;AAAA,kBACT,qBAAqB;AAAA;AAAA,gBACvC,CAAiB;AAAA,cACF;AAAA,YACF;AAAA,YACD,MAAM,MAAM;AAEV,kBAAID,WAAO,QAAA,GAAI;AACbC,8BAAAA,MAAY,MAAA,OAAA,+BAAA,iBAAiB;AAAA,cAC9B;AACD,mBAAK,iBAAiB,iBAAiB;AACvC,sBAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,SAAS;AAAA,gBACT,qBAAqB;AAAA,cACrC,CAAe;AAAA,YACF;AAAA,UACb,CAAW;AAAA,QACX,OAAe;AAEL,eAAK,oBAAoB,OAAO;AAAA,QACjC;AAAA,MACT,OAAa;AAEL,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAAA,kDAAY,gCAAgC;AAAA,QAC7C;AACD,aAAK,iBAAiB,iBAAiB;AACvC,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,qBAAqB;AAAA,QAC/B,CAAS;AAAA,MACF;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,oBAAoB,SAAS;AAC3B,SAAK,iBAAiB,iBAAiB;AAEvCC,kBAAAA,KAAG,eAAe;AAAA,MAChB,MAAM;AAAA;AAAA,MACN,SAAS,CAAC,QAAQ;AAChB,YAAIF,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,OAAA,+BAAY,aAAa,IAAI,QAAQ;AAAA,QACtC;AAED,aAAK,iBAAiB,IAAI;AAC1B,aAAK,iBAAiB,iBAAiB;AAGvCA,sBAAAA,MAAI,eAAe,mBAAmB,IAAI,QAAQ;AAElD,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,UAAU,IAAI;AAAA,UACd,SAAS;AAAA,QACnB,CAAS;AAAA,MACF;AAAA,MACD,MAAM,CAAC,UAAU;AACf,aAAK,iBAAiB,iBAAiB;AAEvC,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,SAAA,+BAAc,qBAAqB,KAAK;AAAA,QACzC;AAGD,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO,MAAM;AAAA,UACb,SAAS;AAAA,UACT,qBAAqB;AAAA;AAAA,QAC/B,CAAS;AAAA,MACF;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,sBAAsB;AACpB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCC,oBAAAA,KAAG,MAAM;AAAA,QACP,SAAS,CAAC,aAAa;AACrB,cAAI,SAAS,MAAM;AACjB,oBAAQ,SAAS,IAAI;AAAA,UACjC,OAAiB;AACL,mBAAO,IAAI,MAAM,YAAY,SAAS,MAAM,CAAC;AAAA,UAC9C;AAAA,QACF;AAAA,QACD,MAAM,CAAC,UAAU;AACf,iBAAO,IAAI,MAAM,YAAY,MAAM,MAAM,CAAC;AAAA,QAC3C;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,mBAAmB,MAAM,UAAU,IAAI;AACrC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACf,cAAQ,kBAAkB;AAGjD,UAAI,cAAc;AAAA,QAChB,SAAS;AAAA,MACjB;AAGM,UAAI,KAAK,gBAAgB;AACvB,oBAAY,WAAW,KAAK,eAAe;AAC3C,oBAAY,aAAa,KAAK,eAAe;AAC7C,YAAIF,WAAO,QAAA,GAAI;AACbC,wBAAAA,MAAA,MAAA,OAAA,+BAAY,aAAY,gBAAgB;AAAA,YACtC,SAAS;AAAA,YACT,aAAa;AAAA,YACb,UAAU,KAAK;AAAA,UAC3B,CAAW;AAAA,QACF;AAAA,MACT,OAAa;AACL,YAAID,WAAO,QAAA,GAAI;AACbC,8BAAA,MAAA,OAAA,+BAAY,iBAAiB,EAAE,SAAS,KAAI,CAAE;AAAA,QAC/C;AAAA,MACF;AAGDE,oBAAI,OAAO,MAAM,WAAW,EACzB,KAAK,CAAC,iBAAiB;AACtB,YAAIH,WAAO,QAAA,GAAI;AACbC,wBAAY,MAAA,MAAA,OAAA,+BAAA,WAAW,YAAY;AAAA,QACpC;AAED,YAAI;AAEF,gBAAM,EAAE,cAAc,YAAY,UAAU,QAAQ,SAAS,YAAY,WAAW,cAAe,IAAG;AAItG,cAAI,cAAc;AAChBG,2BAAAA,UAAU,aAAa;AAAA,cACrB;AAAA,cACA;AAAA,cACA,YAAY;AAAA,YAC5B,CAAe;AAAA,UACF;AAGD,cAAI,eAAe;AACjBA,qCAAU,gBAAgB,aAAa;AAAA,UACxC;AAGD,eAAK,cAAc;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACd,CAAa;AAED,eAAK,cAAc,aAAa;AAGhC,cAAI,KAAK,gBAAgB;AACvBH,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC1B,CAAe;AAAA,UACf,OAAmB;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAC1B,CAAe;AAAA,UACF;AAED,kBAAQ;AAAA,YACN,SAAS;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,gBAAgB,KAAK;AAAA;AAAA,UACnC,CAAa;AAAA,QACF,SAAQ,OAAO;AAEd,cAAID,WAAO,QAAA,GAAI;AACbC,8EAAc,aAAa,KAAK;AAAA,UACjC;AACD,eAAK,cAAc,aAAa;AAChC,iBAAO,IAAI,MAAM,UAAU,CAAC;AAAA,QAC7B;AAAA,MACX,CAAS,EACA,MAAM,CAAC,UAAU;AAChB,aAAK,cAAc,aAAa;AAEhC,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,SAAA,+BAAc,gBAAgB,KAAK;AAAA,QACpC;AAGD,eAAO,KAAK;AAAA,MACtB,CAAS;AAAA,IACT,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,UAAI,OAAOC,cAAAA,KAAG,mBAAmB,YAAY;AAC3CA,sBAAAA,KAAG,eAAe;AAAA,UAChB,MAAM;AAAA,UACN,SAAS,CAAC,QAAQ;AAChB,gBAAIF,WAAO,QAAA,GAAI;AACbC,4BAAA,MAAA,MAAA,OAAA,+BAAY,aAAa,IAAI,QAAQ;AAAA,YACtC;AAED,iBAAK,iBAAiB,IAAI;AAC1B,iBAAK,iBAAiB,iBAAiB;AAEvCA,0BAAAA,MAAI,eAAe,mBAAmB,IAAI,QAAQ;AAElD,oBAAQ,IAAI,QAAQ;AAAA,UACrB;AAAA,UACD,MAAM,CAAC,UAAU;AACf,iBAAK,iBAAiB,iBAAiB;AAGvC,gBAAI,eAAe;AACnB,gBAAI,MAAM,UAAU,MAAM,OAAO,SAAS,MAAM,GAAG;AACjD,6BAAe;AAAA,YAC7B,WAAuB,MAAM,UAAU,MAAM,OAAO,SAAS,MAAM,GAAG;AACxD,6BAAe;AAAA,YAChB;AAED,gBAAID,WAAO,QAAA,GAAI;AACbC,4BAAA,MAAA,MAAA,SAAA,+BAAc,qBAAqB,KAAK;AAAA,YACzC;AACD,mBAAO,IAAI,MAAM,YAAY,CAAC;AAAA,UAC/B;AAAA,QACX,CAAS;AAAA,MACT,OAAa;AAEL,eAAO,IAAI,MAAM,0CAA0C,CAAC;AAAA,MAC7D;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,mBAAmB,OAAO;AACxB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACF,cAAM,EAAE,UAAS,IAAK,MAAM;AAC5B,YAAI,CAAC,WAAW;AACd,iBAAO,IAAI,MAAM,UAAU,CAAC;AAC5B;AAAA,QACD;AAED,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,SAAS;AAAA,QACjC;AAGD,cAAM,kBAAkB,KAAK,kBAAkB;AAC/C,cAAM,kBAAkB;AAAA,UACtB,GAAG;AAAA,UACH;AAAA,UACA,YAAY,KAAK,IAAK;AAAA,QAChC;AAEQ,aAAK,iBAAiB;AACtBA,sBAAAA,MAAI,eAAe,mBAAmB,eAAe;AAErD,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA,UAAU;AAAA,QACpB,CAAS;AAAA,MACF,SAAQ,OAAO;AACd,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAAA,QACjC;AACD,eAAO,IAAI,MAAM,cAAc,MAAM,OAAO,CAAC;AAAA,MAC9C;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,oBAAoB,UAAU;AAC5B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACF,YAAI,CAAC,YAAY,SAAS,KAAI,EAAG,WAAW,GAAG;AAC7C,iBAAO,IAAI,MAAM,QAAQ,CAAC;AAC1B;AAAA,QACD;AAGD,YAAI,SAAS,SAAS,IAAI;AACxB,iBAAO,IAAI,MAAM,eAAe,CAAC;AACjC;AAAA,QACD;AAED,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,QAAQ;AAAA,QAChC;AAGD,cAAM,kBAAkB,KAAK,kBAAkB;AAC/C,cAAM,kBAAkB;AAAA,UACtB,GAAG;AAAA,UACH,UAAU,SAAS,KAAM;AAAA,UACzB,YAAY,KAAK,IAAK;AAAA,QAChC;AAEQ,aAAK,iBAAiB;AACtB,aAAK,iBAAiB,iBAAiB;AACvCA,sBAAAA,MAAI,eAAe,mBAAmB,eAAe;AAErD,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,UAAU,SAAS,KAAM;AAAA,UACzB,UAAU;AAAA,QACpB,CAAS;AAAA,MACF,SAAQ,OAAO;AACd,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAAA,QACjC;AACD,eAAO,IAAI,MAAM,cAAc,MAAM,OAAO,CAAC;AAAA,MAC9C;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,EAAE,cAAc,YAAY,UAAU,QAAQ,SAAS,YAAY,aAAa;AAC5F,QAAI;AACF,YAAM,cAAc,KAAK;AAEzB,UAAI,cAAc;AAChBA,sBAAAA,MAAI,eAAe,gBAAgB,YAAY;AAG/C,cAAM,YAAY,cAAc;AAChCA,sBAAAA,MAAI,eAAe,cAAc,SAAS;AAG1C,cAAM,aAAa,YACjB,cAAe,YAAY,MAC3B,cAAe,IAAI,KAAK,KAAK,KAAK;AACpCA,sBAAAA,MAAI,eAAe,qBAAqB,UAAU;AAAA,MACnD;AAED,UAAI,UAAU;AACZA,sBAAAA,MAAI,eAAe,YAAY,QAAQ;AACvC,aAAK,WAAW;AAAA,MACjB;AAED,UAAI,QAAQ;AACVA,sBAAAA,MAAI,eAAe,UAAU,MAAM;AACnC,aAAK,SAAS;AAAA,MACf;AAED,UAAI,SAAS;AACXA,sBAAAA,MAAI,eAAe,WAAW,OAAO;AACrC,aAAK,UAAU;AAAA,MAChB;AAED,UAAI,YAAY;AACd,aAAK,aAAa;AAAA,MAEnB;AAGDA,oBAAAA,MAAI,eAAe,cAAc,WAAW;AAE5C,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAAA,MAAA,MAAA,OAAA,+BAAY,gBAAgB;AAAA,UAC1B,UAAU,CAAC,CAAC;AAAA,UACZ,aAAa,CAAC,CAAC;AAAA,UACf,WAAW,CAAC,CAAC;AAAA,UACb,YAAY,CAAC,CAAC;AAAA,UACd;AAAA,QACV,CAAS;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,wEAAc,aAAa,KAAK;AAChC,YAAM,IAAI,MAAM,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AACf,QAAI;AAEF,WAAK,iBAAgB;AAGrB,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACR;AAEM,mBAAa,QAAQ,SAAO;AAC1B,YAAI;AACFA,8BAAI,kBAAkB,GAAG;AAAA,QAC1B,SAAQ,GAAG;AACV,cAAID,WAAO,QAAA,GAAI;AACbC,gCAAA,MAAA,QAAA,+BAAa,SAAS,GAAG,QAAQ,CAAC;AAAA,UACnC;AAAA,QACF;AAAA,MACT,CAAO;AAGD,WAAK,cAAc,aAAa;AAChC,WAAK,iBAAiB,iBAAiB;AACvC,WAAK,WAAW;AAChB,WAAK,iBAAiB;AACtB,WAAK,aAAa;AAClB,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,kBAAkB;AAEvB,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAAA,kDAAY,WAAW;AAAA,MACxB;AAAA,IACF,SAAQ,OAAO;AACdA,wEAAc,aAAa,KAAK;AAChC,YAAM,IAAI,MAAM,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AACf,WAAO;AAAA,MACL,aAAa,KAAK;AAAA,MAClB,gBAAgB,KAAK;AAAA,IAC3B;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AAClB,QAAI;AACF,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,iBAAiB;AAC3D,YAAM,YAAYA,cAAAA,MAAI,eAAe,YAAY;AACjD,YAAM,kBAAkBA,cAAAA,MAAI,eAAe,mBAAmB;AAE9D,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,kBAAkB,KAAK,IAAK,IAAG,kBAAkB;AAAA,QAC/D,qBAAqB,CAAC,EAAE,kBAAkB,eAAe,YAAY,eAAe;AAAA,MAC5F;AAAA,IACK,SAAQ,OAAO;AACdA,oBAAA,MAAA,MAAA,SAAA,+BAAc,gBAAgB,KAAK;AACnC,aAAO;AAAA,QACL,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,qBAAqB;AAAA,MAC7B;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB;AACpB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,KAAK;AAErB,WAAO;AAAA;AAAA,MAEL;AAAA,MACA;AAAA,MACA,WAAW,WAAW;AAAA,MACtB,iBAAiB,WAAW;AAAA,MAC5B,cAAc,WAAW;AAAA;AAAA,MAGzB,UAAU,WAAW;AAAA;AAAA,MAGrB,gBAAgB,WAAW;AAAA;AAAA,MAG3B,aAAa,KAAK;AAAA,MAClB,gBAAgB,KAAK;AAAA,MACrB,qBAAqB,WAAW;AAAA,IACtC;AAAA,EACG;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACV,WAAO,KAAK,UAAUA,cAAG,MAAC,eAAe,QAAQ;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa;AACX,WAAO,KAAK,WAAWA,cAAG,MAAC,eAAe,SAAS;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACb,UAAM,EAAE,oBAAmB,IAAK,KAAK,kBAAiB;AACtD,WAAO,CAAC;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU;AACR,SAAK,iBAAgB;AACrB,SAAK,eAAc;AAAA,EACpB;AACH;AAGK,MAAC,aAAa,IAAI,WAAa;AAe7B,SAAS,aAAa;AAC3B,SAAO,WAAW;AACpB;AAKO,SAAS,SAAS;AACvB,aAAW,eAAc;AACzB,SAAO,QAAQ,QAAQ,EAAE,SAAS,KAAM,CAAA;AAC1C;AAiCO,SAAS,eAAe;AAC7B,SAAO,WAAW;AACpB;AAYO,SAAS,eAAe,UAAU,IAAI;AAC3C,SAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAC5C,QAAI;AACF,YAAM,cAAc,MAAM,WAAW;AAErC,UAAI,YAAY,SAAS;AAEvB,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAW,oBAAqB;AAAA,UAC1C,cAAc,WAAW,aAAc;AAAA,QACjD,CAAS;AAAA,MACT,WAAiB,QAAQ,cAAc,OAAO;AAEtC,YAAI;AACF,gBAAM,cAAc,MAAM,WAAW,MAAM,OAAO;AAClD,kBAAQ;AAAA,YACN,SAAS;AAAA,YACT,UAAU,WAAW,oBAAqB;AAAA,YAC1C,cAAc,WAAW,aAAc;AAAA,YACvC,kBAAkB;AAAA,UAC9B,CAAW;AAAA,QACF,SAAQ,YAAY;AACnB,kBAAQ;AAAA,YACN,SAAS;AAAA,YACT,WAAW;AAAA,YACX,iBAAiB;AAAA,YACjB,OAAO,WAAW;AAAA,UAC9B,CAAW;AAAA,QACF;AAAA,MACT,OAAa;AAEL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,UACX,QAAQ,YAAY;AAAA,QAC9B,CAAS;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACd,aAAO,KAAK;AAAA,IACb;AAAA,EACL,CAAG;AACH;;;;;;"}