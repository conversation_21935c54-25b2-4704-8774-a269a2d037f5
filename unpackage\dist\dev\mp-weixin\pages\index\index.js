"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../config/env.js");
if (!Array) {
  const _component_open_data_item = common_vendor.resolveComponent("open-data-item");
  const _component_open_data_list = common_vendor.resolveComponent("open-data-list");
  (_component_open_data_item + _component_open_data_list)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const gridData = common_vendor.ref({
      mediationQuery: null,
      realCase: null,
      mediationComplaint: null
    });
    common_vendor.onMounted(() => {
      fetchGridData();
    });
    function fetchGridData() {
      setTimeout(() => {
        gridData.value = {
          mediationQuery: {
            subtitle: "查看您的调解进度和历史记录"
          },
          realCase: {
            subtitle: "了解成功调解案例和经验"
          },
          mediationComplaint: {
            subtitle: "提交意见反馈和服务投诉"
          }
        };
      }, 500);
    }
    function navigateTo(type) {
      const routes = {
        mediationQuery: "/pages/mediation_query/mediation_query",
        realCase: "/pages/real_case/real_case",
        mediationComplaint: "/pages/mediation_complaint/mediation_complaint"
      };
      if (routes[type]) {
        common_vendor.index.navigateTo({
          url: routes[type]
        });
      } else {
        common_vendor.index.showToast({
          title: "功能建设中...",
          icon: "none"
        });
      }
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          type: "userAvatar",
          index: "{{index}}"
        }),
        b: common_vendor.p({
          type: "userNickName",
          index: "{{index}}"
        }),
        c: common_vendor.p({
          type: "groupMembers",
          members: "{{members}}"
        }),
        d: gridData.value.mediationQuery
      }, gridData.value.mediationQuery ? {
        e: common_vendor.t(gridData.value.mediationQuery.subtitle)
      } : {}, {
        f: common_vendor.o(($event) => navigateTo("mediationQuery")),
        g: gridData.value.realCase
      }, gridData.value.realCase ? {
        h: common_vendor.t(gridData.value.realCase.subtitle)
      } : {}, {
        i: common_vendor.o(($event) => navigateTo("realCase")),
        j: gridData.value.mediationComplaint
      }, gridData.value.mediationComplaint ? {
        k: common_vendor.t(gridData.value.mediationComplaint.subtitle)
      } : {}, {
        l: common_vendor.o(($event) => navigateTo("mediationComplaint"))
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
