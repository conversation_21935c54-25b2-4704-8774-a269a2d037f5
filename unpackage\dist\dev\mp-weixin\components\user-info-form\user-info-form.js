"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_wechatAuth = require("../../utils/wechat-auth.js");
const utils_avatarHelper = require("../../utils/avatar-helper.js");
const utils_errorHandler = require("../../utils/error-handler.js");
const utils_wechatErrorHandler = require("../../utils/wechat-error-handler.js");
const _sfc_main = {
  __name: "user-info-form",
  props: {
    // 是否允许跳过
    allowSkip: {
      type: Boolean,
      default: false
    },
    // 提交按钮文案
    submitButtonText: {
      type: String,
      default: "完成设置"
    },
    // 初始用户信息
    initialUserInfo: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["submit", "skip", "avatar-change", "nickname-change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const isSubmitting = common_vendor.ref(false);
    const tempAvatarPaths = common_vendor.ref([]);
    const userInfo = common_vendor.ref({
      avatarUrl: "",
      nickName: "",
      ...props.initialUserInfo
    });
    const defaultAvatarUrl = common_vendor.computed(() => {
      return utils_avatarHelper.getDefaultAvatar("default", userInfo.value.gender);
    });
    const avatarPreviewUrl = common_vendor.computed(() => {
      return utils_avatarHelper.createAvatarPreviewUrl(userInfo.value.avatarUrl) || defaultAvatarUrl.value;
    });
    const canSubmit = common_vendor.computed(() => {
      return userInfo.value.nickName && userInfo.value.nickName.trim().length > 0 && userInfo.value.avatarUrl && !isSubmitting.value;
    });
    common_vendor.onMounted(() => {
      const storedInfo = utils_wechatAuth.wechatAuth.getStoredUserInfo();
      if (storedInfo.wechatUserInfo) {
        userInfo.value = {
          ...userInfo.value,
          ...storedInfo.wechatUserInfo
        };
      }
    });
    common_vendor.watch(() => userInfo.value, (newVal) => {
      emit("avatar-change", newVal.avatarUrl);
      emit("nickname-change", newVal.nickName);
    }, { deep: true });
    function onAvatarLoadError() {
      const fallbackUrl = utils_avatarHelper.handleAvatarError(userInfo.value.avatarUrl, {
        gender: userInfo.value.gender,
        useGenderDefault: true
      });
      userInfo.value.avatarUrl = fallbackUrl;
    }
    async function onChooseAvatar(event) {
      const result = await utils_errorHandler.withErrorBoundary(async () => {
        const chooseResult = await utils_wechatAuth.wechatAuth.handleChooseAvatar(event);
        if (chooseResult.success) {
          if (chooseResult.avatarUrl && chooseResult.avatarUrl.includes("tmp")) {
            tempAvatarPaths.value.push(chooseResult.avatarUrl);
          }
          userInfo.value.avatarUrl = chooseResult.avatarUrl;
          common_vendor.index.showToast({
            title: "头像设置成功",
            icon: "success",
            duration: 1500
          });
          return chooseResult;
        } else {
          throw new Error("头像选择失败");
        }
      }, {
        showError: false,
        // 使用微信专用错误处理
        onError: (errorAnalysis) => {
          common_vendor.index.__f__("error", "at components/user-info-form/user-info-form.vue:179", "头像选择失败:", errorAnalysis);
          utils_wechatErrorHandler.showWechatError(errorAnalysis.originalMessage || "头像选择失败");
        }
      });
      if (!result.success) {
        common_vendor.index.__f__("warn", "at components/user-info-form/user-info-form.vue:187", "头像选择操作失败");
      }
    }
    function onNicknameInput(event) {
      const value = event.detail.value;
      userInfo.value.nickName = value;
    }
    async function onNicknameBlur() {
      const nickname = userInfo.value.nickName;
      if (!nickname || nickname.trim().length === 0) {
        return;
      }
      await utils_errorHandler.withErrorBoundary(async () => {
        await utils_wechatAuth.wechatAuth.handleNicknameInput(nickname);
      }, {
        showError: false,
        // 使用微信专用错误处理
        onError: (errorAnalysis) => {
          common_vendor.index.__f__("error", "at components/user-info-form/user-info-form.vue:209", "昵称处理失败:", errorAnalysis);
          utils_wechatErrorHandler.showWechatError(errorAnalysis.originalMessage || "昵称设置失败");
        }
      });
    }
    async function onSubmit() {
      if (!canSubmit.value || isSubmitting.value) {
        return;
      }
      if (!userInfo.value.nickName || userInfo.value.nickName.trim().length === 0) {
        common_vendor.index.showToast({
          title: "请输入昵称",
          icon: "none"
        });
        return;
      }
      if (!userInfo.value.avatarUrl) {
        common_vendor.index.showToast({
          title: "请选择头像",
          icon: "none"
        });
        return;
      }
      try {
        isSubmitting.value = true;
        await utils_wechatAuth.wechatAuth.handleNicknameInput(userInfo.value.nickName);
        emit("submit", {
          success: true,
          userInfo: userInfo.value
        });
        common_vendor.index.showToast({
          title: "设置完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at components/user-info-form/user-info-form.vue:257", "提交用户信息失败:", error);
        common_vendor.index.showToast({
          title: error.message || "设置失败",
          icon: "none",
          duration: 2e3
        });
        emit("submit", {
          success: false,
          error: error.message
        });
      } finally {
        isSubmitting.value = false;
      }
    }
    function onSkip() {
      if (isSubmitting.value) {
        return;
      }
      emit("skip", {
        userInfo: userInfo.value
      });
    }
    common_vendor.onUnmounted(() => {
      if (tempAvatarPaths.value.length > 0) {
        utils_avatarHelper.cleanupTempAvatars(tempAvatarPaths.value);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: avatarPreviewUrl.value,
        b: common_vendor.o(onAvatarLoadError),
        c: common_vendor.o(onChooseAvatar),
        d: isSubmitting.value,
        e: common_vendor.o(onNicknameBlur),
        f: common_vendor.o([($event) => userInfo.value.nickName = $event.detail.value, onNicknameInput]),
        g: isSubmitting.value,
        h: userInfo.value.nickName,
        i: isSubmitting.value
      }, isSubmitting.value ? {} : {
        j: common_vendor.t(__props.submitButtonText)
      }, {
        k: !canSubmit.value || isSubmitting.value ? 1 : "",
        l: !canSubmit.value || isSubmitting.value,
        m: common_vendor.o(onSubmit),
        n: __props.allowSkip
      }, __props.allowSkip ? {
        o: common_vendor.o(onSkip),
        p: isSubmitting.value
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5416efb5"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/user-info-form/user-info-form.js.map
