"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../config/env.js");
if (!Array) {
  const _easycom_uni_data_checkbox2 = common_vendor.resolveComponent("uni-data-checkbox");
  _easycom_uni_data_checkbox2();
}
const _easycom_uni_data_checkbox = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.js";
if (!Math) {
  (_easycom_uni_data_checkbox + CanvasAutograph)();
}
const CanvasAutograph = () => "../../components/canvas-autograph/canvas-autograph.js";
const _sfc_main = {
  __name: "agreement_signing",
  setup(__props) {
    const workOrderData = common_vendor.ref({
      id: "MED20230001",
      status: "进行中",
      // 修改为与图片一致的状态
      createDate: "2025-11-01"
    });
    const isSigned = common_vendor.ref(false);
    const isLoadingPreview = common_vendor.ref(false);
    const preview = common_vendor.computed(() => {
      return {
        icon: "fas fa-expand",
        text: "全屏查看协议内容",
        class: "preview-btn"
      };
    });
    const agreementStatus = common_vendor.ref([]);
    const signatureData = common_vendor.ref("");
    const isLoading = common_vendor.ref(false);
    const hobbys = common_vendor.ref([
      {
        text: "我已阅读并同意《调解协议》的全部条款",
        value: "1",
        disabled: false
      }
    ]);
    const checkboxData = common_vendor.computed(() => {
      if (isSigned.value) {
        return [
          {
            text: "协议签署完成",
            value: 0,
            disabled: true
          }
        ];
      }
      return hobbys.value;
    });
    const buttonConfig = common_vendor.computed(() => {
      if (isSigned.value) {
        return {
          icon: "fas fa-check-circle",
          text: "协议签署完成",
          class: "success-btn"
        };
      }
      return {
        icon: "fas fa-file-signature",
        text: "确认协议签署",
        class: !agreementStatus.value.includes("1") ? "disabled-btn" : "sign-btn"
      };
    });
    const saveSignatureState = () => {
      try {
        const stateData = {
          isSigned: isSigned.value,
          agreementStatus: agreementStatus.value,
          signatureData: signatureData.value,
          timestamp: Date.now()
        };
        common_vendor.index.setStorageSync("agreement_signature_state", JSON.stringify(stateData));
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:224", "保存签名状态失败:", error);
      }
    };
    const loadSignatureState = () => {
      try {
        const savedState = common_vendor.index.getStorageSync("agreement_signature_state");
        if (savedState) {
          const stateData = JSON.parse(savedState);
          if (Date.now() - stateData.timestamp < 24 * 60 * 60 * 1e3) {
            isSigned.value = stateData.isSigned || false;
            agreementStatus.value = stateData.agreementStatus || [];
            signatureData.value = stateData.signatureData || "";
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:244", "加载签名状态失败:", error);
      }
    };
    const handlePreview = async () => {
      try {
        isLoadingPreview.value = true;
        await new Promise((resolve) => setTimeout(resolve, 1500));
        common_vendor.index.navigateTo({
          url: "/pages/protocol_preview/protocol_preview"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:263", "跳转失败:", error);
        common_vendor.index.showToast({
          title: "跳转失败，请重试",
          icon: "error"
        });
      } finally {
        isLoadingPreview.value = false;
      }
    };
    const handleConfirm = async () => {
      try {
        if (isSigned.value) {
          isLoading.value = true;
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          common_vendor.index.showToast({
            title: "协议确认完成",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages/contact_information/contact_information"
            });
          }, 1500);
          return;
        }
        if (agreementStatus.value.includes("1")) {
          common_vendor.index.showModal({
            title: "提示",
            content: "您已同意协议条款，请进行电子签名以完成签署流程",
            confirmText: "去签名",
            cancelText: "稍后",
            success: (res) => {
              if (res.confirm) {
                openCanvas();
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "请先勾选同意协议条款",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:323", "处理确认操作失败:", error);
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "error"
        });
      } finally {
        isLoading.value = false;
      }
    };
    const isCanvas = common_vendor.ref(false);
    const complete = async (signatureBase64) => {
      try {
        isLoading.value = true;
        common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:347", "签名数据:", signatureBase64);
        signatureData.value = signatureBase64;
        isSigned.value = true;
        agreementStatus.value = ["1"];
        saveSignatureState();
        common_vendor.index.showToast({
          title: "签名保存成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:364", "处理签名失败:", error);
        common_vendor.index.showToast({
          title: "签名保存失败，请重试",
          icon: "error"
        });
      } finally {
        isLoading.value = false;
      }
    };
    const openCanvas = () => {
      try {
        if (isSigned.value) {
          common_vendor.index.showToast({
            title: "协议已签署，无需重复操作",
            icon: "none"
          });
          return;
        }
        if (isLoading.value) {
          common_vendor.index.showToast({
            title: "正在处理中，请稍候",
            icon: "loading"
          });
          return;
        }
        isCanvas.value = true;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:398", "打开签名界面失败:", error);
        common_vendor.index.showToast({
          title: "打开签名界面失败",
          icon: "error"
        });
      }
    };
    common_vendor.onMounted(() => {
      loadSignatureState();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(workOrderData.value.id || "MED20230001"),
        b: common_vendor.t(workOrderData.value.status || "进行中"),
        c: workOrderData.value.status === "进行中" ? 1 : "",
        d: common_vendor.t(workOrderData.value.createDate || "2023-11-01"),
        e: isLoadingPreview.value
      }, isLoadingPreview.value ? {} : {
        f: common_vendor.n(preview.value.icon)
      }, {
        g: common_vendor.t(isLoadingPreview.value ? "正在加载PDF" : preview.value.text),
        h: common_vendor.o(handlePreview),
        i: common_vendor.t(isSigned.value ? "协议签署已完成" : "请勾选下方确认项进行电子签署"),
        j: common_vendor.o(($event) => agreementStatus.value = $event),
        k: common_vendor.p({
          multiple: true,
          localdata: checkboxData.value,
          disabled: isSigned.value,
          modelValue: agreementStatus.value
        }),
        l: common_vendor.t(isSigned.value ? "电子签名已生效，协议具有法律效力" : "点击此处进入电子签名界面"),
        m: isSigned.value ? 1 : "",
        n: isSigned.value ? 1 : "",
        o: common_vendor.o(openCanvas),
        p: isLoading.value
      }, isLoading.value ? {} : {
        q: common_vendor.n(buttonConfig.value.icon)
      }, {
        r: common_vendor.t(isLoading.value ? "正在确认..." : buttonConfig.value.text),
        s: common_vendor.n(buttonConfig.value.class),
        t: common_vendor.o(handleConfirm),
        v: isLoading.value || !isSigned.value && !agreementStatus.value.includes("1"),
        w: common_vendor.o(complete),
        x: common_vendor.o(($event) => isCanvas.value = $event),
        y: common_vendor.p({
          showSignatureLine: true,
          signatureLabel: "签名：",
          signatureLineY: 60,
          signatureLineColor: "#2979ff",
          modelValue: isCanvas.value
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dd2a7f81"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/agreement_signing/agreement_signing.js.map
