"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "protocol_preview",
  setup(__props) {
    const fileUrl = common_vendor.ref("http://192.168.1.101:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx");
    const pdfImagesBaseUrl = common_vendor.ref("http://192.168.1.101:10010/pdf_images/scheme/485/");
    const showImagePreview = common_vendor.ref(false);
    const pdfImages = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const loadedImages = common_vendor.ref(/* @__PURE__ */ new Set());
    const scrollTop = common_vendor.ref(0);
    const scrollProgress = common_vendor.ref(0);
    common_vendor.ref(0);
    common_vendor.ref(0);
    const viewAsImages = async () => {
      try {
        isLoading.value = true;
        showImagePreview.value = true;
        const images = await getPdfImages();
        if (images.length > 0) {
          pdfImages.value = images;
        } else {
          common_vendor.index.showToast({
            title: "暂无可预览内容",
            icon: "none"
          });
          closeImagePreview();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:121", "加载图片预览失败:", error);
        common_vendor.index.showToast({
          title: "加载失败，请稍后重试",
          icon: "error"
        });
        closeImagePreview();
      } finally {
        isLoading.value = false;
      }
    };
    const getPdfImages = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      return [
        { url: `${pdfImagesBaseUrl.value}page_1.jpg`, page: 1 },
        { url: `${pdfImagesBaseUrl.value}page_2.jpg`, page: 2 },
        { url: `${pdfImagesBaseUrl.value}page_3.jpg`, page: 3 },
        { url: `${pdfImagesBaseUrl.value}page_4.jpg`, page: 4 },
        { url: `${pdfImagesBaseUrl.value}page_5.jpg`, page: 5 }
      ];
    };
    const onScroll = (e) => {
      const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;
      scrollTop.value = currentScrollTop;
      if (scrollHeight > clientHeight) {
        scrollProgress.value = currentScrollTop / (scrollHeight - clientHeight) * 100;
      }
    };
    const onImageLoad = (index) => {
      loadedImages.value.add(index);
      common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:169", `图片 ${index + 1} 加载成功`);
    };
    const onImageError = (index) => {
      common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:176", `图片 ${index + 1} 加载失败`);
      common_vendor.index.showToast({
        title: `第${index + 1}页加载失败`,
        icon: "none",
        duration: 2e3
      });
    };
    const closeImagePreview = () => {
      showImagePreview.value = false;
      scrollTop.value = 0;
      scrollProgress.value = 0;
      loadedImages.value.clear();
    };
    const downloadFile = () => {
      common_vendor.index.showLoading({ title: "正在下载..." });
      common_vendor.index.downloadFile({
        url: fileUrl.value,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              success: () => {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "打开成功",
                  icon: "success"
                });
              },
              fail: (error) => {
                common_vendor.index.hideLoading();
                common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:216", "打开文档失败:", error);
                common_vendor.index.showToast({
                  title: "打开失败，请检查是否安装PDF阅读器",
                  icon: "none",
                  duration: 3e3
                });
              }
            });
          }
        },
        fail: (error) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:228", "下载失败:", error);
          common_vendor.index.showToast({
            title: "下载失败，请检查网络连接",
            icon: "error"
          });
        }
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.setNavigationBarTitle({ title: "协议预览" });
      viewAsImages();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: showImagePreview.value
      }, showImagePreview.value ? common_vendor.e({
        b: common_vendor.t(Math.round(scrollProgress.value)),
        c: isLoading.value
      }, isLoading.value ? {} : {
        d: common_vendor.f(pdfImages.value, (image, index, i0) => {
          return {
            a: index,
            b: image.url,
            c: index === 0 ? 1 : "",
            d: common_vendor.o(($event) => onImageLoad(index), index),
            e: common_vendor.o(($event) => onImageError(index), index)
          };
        })
      }, {
        e: common_vendor.o(downloadFile),
        f: scrollTop.value,
        g: common_vendor.o(onScroll),
        h: scrollProgress.value + "%"
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f6102ab"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/protocol_preview/protocol_preview.js.map
