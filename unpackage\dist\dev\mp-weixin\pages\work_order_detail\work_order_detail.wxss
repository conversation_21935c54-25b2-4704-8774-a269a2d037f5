/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.work-order-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.work-order-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.work-order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.work-order-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.work-order-status {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}
.status-label {
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #999;
  color: #fff;
}
.status-pending {
  background-color: #faad14;
}
.work-order-date {
  font-size: 28rpx;
  color: #666;
}

/* .progress-bar {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
} */
.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
}
.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}
.step-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}
.step-line {
  position: absolute;
  top: 30rpx;
  left: 50%;
  right: -50%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}
.progress-step:last-child .step-line {
  display: none;
}
.step-label {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.progress-step.active .step-circle {
  background-color: #2979ff;
}
.progress-step.active .step-label {
  color: #2979ff;
  font-weight: bold;
}
.info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
}

/* .section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 8rpx;
	height: 32rpx;
	background-color: #2979ff;
	border-radius: 4rpx;
} */
.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx 0;
  color: #333;
  font-size: 32rpx;
}
.info-item:last-child {
  border-bottom: none;
}
.info-label {
  font-weight: 500;
}
.file-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 28rpx 0;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
  transition: 0.3s;
}
.file-item .fas {
  margin-right: 24rpx;
}
.file-item:last-child {
  border-bottom: none;
}
.file-content {
  min-width: 0;
  display: flex;
  align-items: center;
  flex: 1 1 0%;
}
.file-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
  word-break: break-all;
  transition: color 0.3s;
}
.action-buttons {
  display: flex;
  gap: 30rpx;
}
.reject-button,
.accept-button {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  border-radius: 16rpx;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px;
  font-size: 32rpx;
}
.reject-button {
  background-color: #fff;
  color: #3b7eeb;
  border: 2rpx solid #3b7eeb;
}
.accept-button {
  background-color: #3b7eeb;
  color: #fff;
}
.work-closing {
  background-color: #fff8f8;
  border: 2rpx solid #ffeded;
}
.work-closing .section-title {
  font-size: 36rpx;
  color: #f5222d;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.work-closing .section-title .fas {
  color: #f5222d;
  font-size: 40rpx;
  margin-right: 20rpx;
}
.work-closing .section-tip {
  font-size: 32rpx;
  color: #666;
}