{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"index-container\">\r\n\t\t<open-data-list type=\"groupMembers\" members=\"{{members}}\">\r\n\t\t\t<view class=\"userinfo\" slot:index>\r\n\t\t\t\t<open-data-item class=\"avatar \" type=\"userAvatar\" index=\"{{index}}\" />\r\n\t\t\t\t<open-data-item class=\"\" type=\"userNickName\" index=\"{{index}}\" />\r\n\t\t\t</view>\r\n\t\t</open-data-list>\r\n\t\t<view class=\"welcome-banner-minimal\">\r\n\t\t\t<view class=\"banner-minimal-content\">\r\n\t\t\t\t<view class=\"banner-minimal-left\">\r\n\t\t\t\t\t<view class=\"banner-minimal-lines\">\r\n\t\t\t\t\t\t<view class=\"line line-1\"></view>\r\n\t\t\t\t\t\t<view class=\"line line-2\"></view>\r\n\t\t\t\t\t\t<view class=\"line line-3\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"banner-minimal-right\">\r\n\t\t\t\t\t<h1 class=\"banner-minimal-title\">华泰民商事调解中心</h1>\r\n\t\t\t\t\t<p class=\"banner-minimal-subtitle\">为您服务</p>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"banner-minimal-decoration\"></view>\r\n\t\t</view>\r\n\t\t<view class=\"grid-container\">\r\n\t\t\t<view class=\"grid-item\" @click=\"navigateTo('mediationQuery')\">\r\n\t\t\t\t<view class=\"grid-icon\">\r\n\t\t\t\t\t<i class=\"fas fa-search\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"grid-item-content\">\r\n\t\t\t\t\t<view class=\"grid-title\">调解查询</view>\r\n\t\t\t\t\t<view class=\"grid-subtitle\" v-if=\"gridData.mediationQuery\">{{gridData.mediationQuery.subtitle}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<i class=\"fas fa-chevron-right feature-arrow\"></i>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"grid-item\" @click=\"navigateTo('realCase')\">\r\n\t\t\t\t<view class=\"grid-icon\">\r\n\t\t\t\t\t<i class=\"fas fa-file-alt\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"grid-item-content\">\r\n\t\t\t\t\t<view class=\"grid-title\">案例展示</view>\r\n\t\t\t\t\t<view class=\"grid-subtitle\" v-if=\"gridData.realCase\">{{gridData.realCase.subtitle}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<i class=\"fas fa-chevron-right feature-arrow\"></i>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"grid-item\" @click=\"navigateTo('mediationComplaint')\">\r\n\t\t\t\t<view class=\"grid-icon\">\r\n\t\t\t\t\t<i class=\"fas fa-comments\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"grid-item-content\">\r\n\t\t\t\t\t<view class=\"grid-title\">投诉建议</view>\r\n\t\t\t\t\t<view class=\"grid-subtitle\" v-if=\"gridData.mediationComplaint\">{{gridData.mediationComplaint.subtitle}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<i class=\"fas fa-chevron-right feature-arrow\"></i>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\n// 导入API工具类\r\nimport { api } from '@/utils/api.js';\r\n/* \r\n登录成功后：调用 api.auth.setToken(token) 保存token\r\n页面跳转传参：可通过URL参数传递token，系统会自动识别并保存\r\n退出登录：调用 api.auth.clearToken() 清除所有认证信息\r\n\r\n\r\n// 设置token\r\napi.auth.setToken('your_token_here');\r\n\r\n// 获取token（支持多来源自动获取）\r\nconst token = api.auth.getToken();\r\n\r\n// 检查token是否存在\r\nconst hasToken = api.auth.hasToken();\r\n\r\n// 清除token\r\napi.auth.clearToken(); */\r\n// 响应式数据\r\nconst gridData = ref({\r\n\tmediationQuery: null,\r\n\trealCase: null,\r\n\tmediationComplaint: null,\r\n});\r\n\r\n// 页面加载时执行\r\nonMounted(() => {\r\n\tfetchGridData();\r\n});\r\n\r\n\r\n// 获取网格数据\r\nfunction fetchGridData() {\r\n\t// 这里应该调用实际的API获取数据\r\n\t// 暂时使用模拟数据\r\n\tsetTimeout(() => {\r\n\t\tgridData.value = {\r\n\t\t\tmediationQuery: {\r\n\t\t\t\tsubtitle: '查看您的调解进度和历史记录'\r\n\t\t\t},\r\n\t\t\trealCase: {\r\n\t\t\t\tsubtitle: '了解成功调解案例和经验'\r\n\t\t\t},\r\n\t\t\tmediationComplaint: {\r\n\t\t\t\tsubtitle: '提交意见反馈和服务投诉'\r\n\t\t\t},\r\n\t\t};\r\n\t}, 500);\r\n}\r\n\r\n// 页面导航\r\nfunction navigateTo(type) {\r\n\tconst routes = {\r\n\t\tmediationQuery: '/pages/mediation_query/mediation_query',\r\n\t\trealCase: '/pages/real_case/real_case',\r\n\t\tmediationComplaint: '/pages/mediation_complaint/mediation_complaint',\r\n\t};\r\n\t\r\n\tif (routes[type]) {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: routes[type]\r\n\t\t});\r\n\t}else{\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '功能建设中...',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t:root {\r\n\t\t--primary-color: #3b7eeb;\r\n\t\t--primary-light: #e6f0ff;\r\n\t\t--primary-dark: #2c62c9;\r\n\t\t--secondary-color: #f5f7fa;\r\n\t\t--success-color: #52c41a;\r\n\t\t--warning-color: #faad14;\r\n\t\t--danger-color: #f5222d;\r\n\t\t--info-color: #1890ff;\r\n\t\t--text-color: #333333;\r\n\t\t--text-secondary: #666666;\r\n\t\t--text-light: #999999;\r\n\t\t--border-color: #e8e8e8;\r\n\t\t--background-color: #f5f7fa;\r\n\t\t--card-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n\t\t--transition-normal: all 0.3s ease;\r\n\t}\r\n\t.index-container {\r\n\t\theight: calc(100% - 94px);\r\n\t\toverflow-y: auto;\r\n\t\tpadding: 30rpx;\r\n\t\tpadding-bottom: 140rpx;\r\n\t\tbackground-color: #f8fafc;\r\n\t}\r\n\t\r\n\t.welcome-banner-minimal {\r\n\t\tbackground: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\r\n\t\tborder-radius: 0;\r\n\t\tmargin: -30rpx -30rpx 40rpx -30rpx;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\tpadding: 64rpx 40rpx;\r\n\t}\r\n\t.banner-minimal-content {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t}\r\n\t.banner-minimal-left {\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\t.banner-minimal-lines {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\t.line {\r\n\t\theight: 6rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\tborder-radius: 4rpx;\r\n\t\t// transition: var(--transition-normal);\r\n\t\t// transition: all 0.3s ease;\r\n\t\ttransition-property: all;\r\n\t\ttransition-duration: 3s;\r\n\t\ttransition-timing-function: ease-in-out;\r\n\t}\r\n\t.line-1 {\r\n\t\twidth: 80rpx;\r\n\t\t// animation: line-expand-1 3s ease-in-out infinite;\r\n\t\tanimation-name: line-expand-1;\r\n\t\tanimation-duration: 3s;\r\n\t\tanimation-timing-function: ease-in-out;\r\n\t\tanimation-iteration-count: infinite;\r\n\t}\r\n\t.line-2 {\r\n\t\twidth: 110rpx;\r\n\t\tanimation-name: line-expand-2;\r\n\t\tanimation-duration: 3s;\r\n\t\tanimation-timing-function: ease-in-out;\r\n\t\tanimation-iteration-count: infinite;\r\n\t\tanimation-delay: 0.5s;\r\n\t}\r\n\t.line-3 {\r\n\t\twidth: 60rpx;\r\n\t\tanimation-name: line-expand-3;\r\n\t\tanimation-duration: 3s;\r\n\t\tanimation-timing-function: ease-in-out;\r\n\t\tanimation-iteration-count: infinite;\r\n\t\tanimation-delay: 1s;\r\n\t}\r\n\t.banner-minimal-right {\r\n\t\tflex: 1;\r\n\t\tmargin-left: 48rpx;\r\n\t\ttext-align: left;\r\n\t}\r\n\t.banner-minimal-title {\r\n\t\tcolor: white;\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: 600;\r\n\t\tmargin: 0 0 12rpx 0;\r\n\t\tline-height: 1.3;\r\n\t\tletter-spacing: 1rpx;\r\n\t}\r\n\t.banner-minimal-subtitle {\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin: 0;\r\n\t\tletter-spacing: 4rpx;\r\n\t\tfont-weight: 300;\r\n\t}\r\n\t.banner-minimal-decoration {\r\n\t\tposition: absolute;\r\n\t\ttop: -40rpx;\r\n\t\tright: -40rpx;\r\n\t\twidth: 240rpx;\r\n\t\theight: 240rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.08);\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: float 6s ease-in-out infinite;\r\n\t}\r\n\t.grid-container {\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\t\r\n\t.grid-row {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.grid-item {\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 32rpx;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n\t\ttransition: var(--transition-normal);\r\n\t\tcursor: pointer;\r\n\t\tborder: 2rpx solid rgba(0, 0, 0, 0.03);\r\n\t}\r\n\t.grid-item:hover{\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n\t\ttransform: translateY(-4rpx);\r\n\t}\r\n\t.feature-arrow {\r\n\t\tcolor: var(--text-light);\r\n\t\tfont-size: 28rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\t/* // :first-child 是第一个元素\r\n\t.grid-item:first-child {\r\n\t\tmargin-left: 0;\r\n\t}\r\n\t// :last-child 是最后一个元素\r\n\t.grid-item:last-child {\r\n\t\tmargin-right: 0;\r\n\t} */\r\n\r\n\t.grid-icon {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tbackground-color: var(--primary-light);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 30rpx;\r\n\t\tflex-shrink: 0;\r\n\t\r\n\t\t.fas {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tcolor: var(--primary-color);\r\n\t\t}\r\n\t}\r\n\r\n\t.grid-item-content {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.grid-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: var(--text-color);\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.grid-subtitle {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: var(--text-secondary);\r\n\t\tline-height: 1.3;\r\n\t}\r\n\r\n\t/* 电子签名按钮样式 */\r\n\t.sign-button {\r\n\t\tmargin: 20rpx auto;\r\n\t\twidth: 300rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\ttext-align: center;\r\n\t\tbackground-color: #2979ff;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(41, 121, 255, 0.3);\r\n\t}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;AAmFA,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACpB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,oBAAoB;AAAA,IACrB,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACf;IACD,CAAC;AAID,aAAS,gBAAgB;AAGxB,iBAAW,MAAM;AAChB,iBAAS,QAAQ;AAAA,UAChB,gBAAgB;AAAA,YACf,UAAU;AAAA,UACV;AAAA,UACD,UAAU;AAAA,YACT,UAAU;AAAA,UACV;AAAA,UACD,oBAAoB;AAAA,YACnB,UAAU;AAAA,UACV;AAAA,QACJ;AAAA,MACE,GAAE,GAAG;AAAA,IACP;AAGA,aAAS,WAAW,MAAM;AACzB,YAAM,SAAS;AAAA,QACd,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,oBAAoB;AAAA,MACtB;AAEC,UAAI,OAAO,IAAI,GAAG;AACjBC,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,OAAO,IAAI;AAAA,QACnB,CAAG;AAAA,MACH,OAAM;AACJA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnIA,GAAG,WAAW,eAAe;"}