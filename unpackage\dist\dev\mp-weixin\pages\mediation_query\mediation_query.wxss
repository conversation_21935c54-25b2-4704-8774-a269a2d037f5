/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-d56de527:root {
  --primary-color: #3b7eeb;
  --primary-light: #e6f0ff;
  --success-color: #52c41a;
  --text-color:#333;
}
.mediation-query-container.data-v-d56de527 {
  min-height: 100vh;
  overflow-y: auto;
  background-color: #f8fafc;
  padding: 30rpx 30rpx 140rpx;
}
.search-box.data-v-d56de527 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.search-input-wrapper.data-v-d56de527 {
  flex: 1;
}

/* 调整uni-easyinput的样式 */
.data-v-d56de527 .uni-easyinput__content {
  height: 80rpx;
  background-color: #fff;
  border: 2rpx solid #eee;
  border-radius: 40rpx;
  padding: 0 20rpx;
}
.data-v-d56de527 .uni-icons {
  color: #999;
}
.data-v-d56de527 .uni-easyinput__placeholder-class {
  font-size: 28rpx;
}
.data-v-d56de527 .uni-easyinput__content-input {
  height: 80rpx;
  font-size: 28rpx;
}

/* 未认证状态提示样式 */
.auth-required.data-v-d56de527 {
  --card-background: #ffffff;
}
.auth-required .filter-tabs.data-v-d56de527 {
  display: flex;
  text-align: center;
  overflow-x: auto;
}
.auth-required .filter-tabs .type-card.data-v-d56de527 {
  background-color: #fff;
  color: #666;
  position: relative;
  flex: 1 1 0%;
  border-width: 2rpx;
  border-style: solid;
  border-color: #fff;
  box-shadow: rgba(0, 0, 0, 0.1) 0 4rpx 8rpx;
  border-image: initial;
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
}
.auth-required .filter-tabs .type-card.active.data-v-d56de527 {
  background-color: #2979ff;
  color: #ffffff;
}
.auth-required .form-container.data-v-d56de527 {
  display: flex;
  flex-direction: column;
}
.auth-required .form-container .form-card.data-v-d56de527 {
  overflow: hidden;
}
.auth-required .form-container .form-card .card-header.data-v-d56de527 {
  padding: 30rpx 30rpx 20rpx 10rpx;
  display: flex;
  align-items: center;
}
.auth-required .form-container .form-card .card-header .fas.data-v-d56de527 {
  margin-right: 8px;
  color: var(--primary-color);
}
.auth-required .form-container .form-card .card-header .card-title.data-v-d56de527 {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 800;
}
.auth-required .query-result-card.data-v-d56de527 {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border: 2rpx solid #b7eb8f;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-top: 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.1);
  animation: slideInUp 0.3s ease;
}
.auth-required .query-tip.data-v-d56de527 {
  text-align: center;
  margin-top: 20px;
  font-size: 13px;
  color: var(--text-secondary);
}
.auth-required .certification.data-v-d56de527 {
  color: var(--primary-color);
  text-decoration: none;
}
.auth-required .query-result-icon.data-v-d56de527 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  color: white;
  font-size: 40rpx;
}
.auth-required .query-result-text.data-v-d56de527 {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16rpx;
}
.auth-required .help-text.data-v-d56de527 {
  background-color: var(--primary-light);
  font-size: 26rpx;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  border-width: 2rpx;
  border-style: solid;
  border-color: rgba(59, 126, 235, 0.2);
  border-image: initial;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin: 32rpx 0;
  gap: 16rpx;
}
.auth-required .help-text .fas.data-v-d56de527 {
  margin-right: 10rpx;
  padding-top: 4rpx;
}
.auth-required .auth-button.data-v-d56de527 {
  width: 690rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #2979ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 10rpx;
  margin-top: 40rpx;
}
.data-v-d56de527 .uni-easyinput__content {
  border-radius: 12rpx !important;
}
.order-list.data-v-d56de527 {
  margin-bottom: 20rpx;
}
.order-item.data-v-d56de527 {
  margin-bottom: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: block;
  unicode-bidi: -webkit-isolate;
  unicode-bidi: isolate;
}
.order-item-hover.data-v-d56de527 {
  transform: scale(0.98);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
}
.order-item-animation.data-v-d56de527 {
  animation: fadeIn-d56de527 0.5s ease-out forwards;
}
@keyframes fadeIn-d56de527 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.order-info.data-v-d56de527 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.order-id.data-v-d56de527 {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.order-date.data-v-d56de527 {
  font-size: 28rpx;
  color: #666;
}
.order-date-icon.data-v-d56de527 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.status-label.data-v-d56de527 {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  white-space: nowrap;
}
.arrow-icon.data-v-d56de527 {
  margin-left: 10rpx;
  font-size: 50rpx;
  color: #666;
}
.loading-state.data-v-d56de527 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}
.loading-spinner.data-v-d56de527 {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #2979ff;
  border-radius: 50%;
  animation: spin-d56de527 1s linear infinite;
  margin-bottom: 20rpx;
}
@keyframes spin-d56de527 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-d56de527 {
  font-size: 28rpx;
  color: #999;
}
.empty-state.data-v-d56de527 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}
.empty-image.data-v-d56de527 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.empty-text.data-v-d56de527 {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}
.refresh-button.data-v-d56de527 {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  color: #2979ff;
  background-color: #fff;
  border: 2rpx solid #2979ff;
  border-radius: 35rpx;
}