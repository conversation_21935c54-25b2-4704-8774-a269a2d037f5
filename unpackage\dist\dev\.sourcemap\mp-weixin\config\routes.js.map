{"version": 3, "file": "routes.js", "sources": ["config/routes.js"], "sourcesContent": ["// 路由配置文件\nexport const ROUTES = {\n  // 主要页面\n  INDEX: '/pages/index/index',\n  MINE: '/pages/mine/mine',\n  \n  // 登录相关\n  LOGIN: '/pages/login/login',\n  USER_INFO: '/pages/user-info/user-info',\n  \n  // 认证相关\n  AUTH: '/pages/auth/auth',\n  USER_AGREEMENT: '/pages/user_agreement/user_agreement',\n  PRIVACY_POLICY: '/pages/privacy_policy/privacy_policy',\n  \n  // 业务页面\n  ASSET_LIST: '/pages/asset/list',\n  ASSET_DETAIL: '/pages/asset/detail',\n  ASSET_ADD: '/pages/asset/add',\n  ASSET_EDIT: '/pages/asset/edit',\n  \n  // 其他页面\n  SETTINGS: '/pages/settings/settings',\n  ABOUT: '/pages/about/about'\n};\n\n/**\n * 路由导航器\n */\nexport class RouteNavigator {\n  /**\n   * 跳转到指定页面\n   * @param {string} route 路由路径\n   * @param {Object} params 参数\n   * @param {Object} options 跳转选项\n   */\n  static navigateTo(route, params = {}, options = {}) {\n    const url = this.buildUrl(route, params);\n    \n    uni.navigateTo({\n      url,\n      ...options,\n      success: (res) => {\n        if (options.success) options.success(res);\n      },\n      fail: (err) => {\n        console.error('页面跳转失败:', err);\n        if (options.fail) options.fail(err);\n      }\n    });\n  }\n\n  /**\n   * 重定向到指定页面\n   * @param {string} route 路由路径\n   * @param {Object} params 参数\n   * @param {Object} options 跳转选项\n   */\n  static redirectTo(route, params = {}, options = {}) {\n    const url = this.buildUrl(route, params);\n    \n    uni.redirectTo({\n      url,\n      ...options,\n      success: (res) => {\n        if (options.success) options.success(res);\n      },\n      fail: (err) => {\n        console.error('页面重定向失败:', err);\n        if (options.fail) options.fail(err);\n      }\n    });\n  }\n\n  /**\n   * 重新启动到指定页面\n   * @param {string} route 路由路径\n   * @param {Object} params 参数\n   * @param {Object} options 跳转选项\n   */\n  static reLaunch(route, params = {}, options = {}) {\n    const url = this.buildUrl(route, params);\n    \n    uni.reLaunch({\n      url,\n      ...options,\n      success: (res) => {\n        if (options.success) options.success(res);\n      },\n      fail: (err) => {\n        console.error('页面重启失败:', err);\n        if (options.fail) options.fail(err);\n      }\n    });\n  }\n\n  /**\n   * 切换到Tab页面\n   * @param {string} route 路由路径\n   * @param {Object} options 跳转选项\n   */\n  static switchTab(route, options = {}) {\n    uni.switchTab({\n      url: route,\n      ...options,\n      success: (res) => {\n        if (options.success) options.success(res);\n      },\n      fail: (err) => {\n        console.error('Tab切换失败:', err);\n        if (options.fail) options.fail(err);\n      }\n    });\n  }\n\n  /**\n   * 返回上一页\n   * @param {number} delta 返回层数\n   * @param {Object} options 选项\n   */\n  static navigateBack(delta = 1, options = {}) {\n    uni.navigateBack({\n      delta,\n      ...options,\n      success: (res) => {\n        if (options.success) options.success(res);\n      },\n      fail: (err) => {\n        console.error('页面返回失败:', err);\n        if (options.fail) options.fail(err);\n      }\n    });\n  }\n\n  /**\n   * 构建URL\n   * @param {string} route 路由路径\n   * @param {Object} params 参数\n   * @returns {string} 完整URL\n   */\n  static buildUrl(route, params = {}) {\n    if (!params || Object.keys(params).length === 0) {\n      return route;\n    }\n\n    const queryString = Object.keys(params)\n      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)\n      .join('&');\n\n    return `${route}?${queryString}`;\n  }\n\n  /**\n   * 解析URL参数\n   * @param {string} url URL字符串\n   * @returns {Object} 参数对象\n   */\n  static parseParams(url) {\n    const params = {};\n    const queryIndex = url.indexOf('?');\n    \n    if (queryIndex === -1) {\n      return params;\n    }\n\n    const queryString = url.substring(queryIndex + 1);\n    const pairs = queryString.split('&');\n\n    pairs.forEach(pair => {\n      const [key, value] = pair.split('=');\n      if (key && value) {\n        params[decodeURIComponent(key)] = decodeURIComponent(value);\n      }\n    });\n\n    return params;\n  }\n\n  /**\n   * 检查是否为Tab页面\n   * @param {string} route 路由路径\n   * @returns {boolean} 是否为Tab页面\n   */\n  static isTabPage(route) {\n    const tabPages = [ROUTES.INDEX, ROUTES.MINE];\n    return tabPages.includes(route);\n  }\n\n  /**\n   * 获取当前页面路径\n   * @returns {string} 当前页面路径\n   */\n  static getCurrentRoute() {\n    const pages = getCurrentPages();\n    if (pages.length > 0) {\n      const currentPage = pages[pages.length - 1];\n      return `/${currentPage.route}`;\n    }\n    return '';\n  }\n\n  /**\n   * 智能导航 - 根据页面类型自动选择导航方式\n   * @param {string} route 路由路径\n   * @param {Object} params 参数\n   * @param {Object} options 选项\n   */\n  static smartNavigate(route, params = {}, options = {}) {\n    if (this.isTabPage(route)) {\n      this.switchTab(route, options);\n    } else {\n      this.navigateTo(route, params, options);\n    }\n  }\n}\n\n/**\n * 快捷导航方法\n */\nexport const navigate = {\n  // 主要页面\n  toIndex: () => RouteNavigator.switchTab(ROUTES.INDEX),\n  toMine: () => RouteNavigator.switchTab(ROUTES.MINE),\n  \n  // 登录相关\n  toLogin: () => RouteNavigator.navigateTo(ROUTES.LOGIN),\n  toUserInfo: () => RouteNavigator.navigateTo(ROUTES.USER_INFO),\n  \n  // 认证相关\n  toAuth: () => RouteNavigator.navigateTo(ROUTES.AUTH),\n  toUserAgreement: () => RouteNavigator.navigateTo(ROUTES.USER_AGREEMENT),\n  toPrivacyPolicy: () => RouteNavigator.navigateTo(ROUTES.PRIVACY_POLICY),\n  \n  // 资产相关\n  toAssetList: () => RouteNavigator.navigateTo(ROUTES.ASSET_LIST),\n  toAssetDetail: (id) => RouteNavigator.navigateTo(ROUTES.ASSET_DETAIL, { id }),\n  toAssetAdd: () => RouteNavigator.navigateTo(ROUTES.ASSET_ADD),\n  toAssetEdit: (id) => RouteNavigator.navigateTo(ROUTES.ASSET_EDIT, { id }),\n  \n  // 通用方法\n  back: (delta = 1) => RouteNavigator.navigateBack(delta),\n  smart: (route, params, options) => RouteNavigator.smartNavigate(route, params, options)\n};\n\n/**\n * 路由守卫\n */\nexport class RouteGuard {\n  static beforeEach = null;\n  static afterEach = null;\n\n  /**\n   * 设置路由前置守卫\n   * @param {Function} guard 守卫函数\n   */\n  static setBeforeEach(guard) {\n    this.beforeEach = guard;\n  }\n\n  /**\n   * 设置路由后置守卫\n   * @param {Function} guard 守卫函数\n   */\n  static setAfterEach(guard) {\n    this.afterEach = guard;\n  }\n\n  /**\n   * 执行路由守卫\n   * @param {string} to 目标路由\n   * @param {string} from 来源路由\n   * @returns {boolean} 是否允许导航\n   */\n  static async executeGuards(to, from) {\n    try {\n      if (this.beforeEach) {\n        const result = await this.beforeEach(to, from);\n        if (result === false) {\n          return false;\n        }\n      }\n      return true;\n    } catch (error) {\n      console.error('路由守卫执行失败:', error);\n      return false;\n    }\n  }\n}\n\n// 导出默认路由配置\nexport default {\n  ROUTES,\n  RouteNavigator,\n  navigate,\n  RouteGuard\n};\n"], "names": ["uni"], "mappings": ";;AACO,MAAM,SAAS;AAAA;AAAA,EAEpB,OAAO;AAAA,EACP,MAAM;AAAA;AAAA,EAGN,OAAO;AAAA,EACP,WAAW;AAAA;AAAA,EAGX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,gBAAgB;AAAA;AAAA,EAGhB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA;AAAA,EAGZ,UAAU;AAAA,EACV,OAAO;AACT;AAKO,MAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1B,OAAO,WAAW,OAAO,SAAS,CAAA,GAAI,UAAU,CAAA,GAAI;AAClD,UAAM,MAAM,KAAK,SAAS,OAAO,MAAM;AAEvCA,kBAAAA,MAAI,WAAW;AAAA,MACb;AAAA,MACA,GAAG;AAAA,MACH,SAAS,CAAC,QAAQ;AAChB,YAAI,QAAQ;AAAS,kBAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAc,MAAA,MAAA,SAAA,0BAAA,WAAW,GAAG;AAC5B,YAAI,QAAQ;AAAM,kBAAQ,KAAK,GAAG;AAAA,MACnC;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,WAAW,OAAO,SAAS,CAAA,GAAI,UAAU,CAAA,GAAI;AAClD,UAAM,MAAM,KAAK,SAAS,OAAO,MAAM;AAEvCA,kBAAAA,MAAI,WAAW;AAAA,MACb;AAAA,MACA,GAAG;AAAA,MACH,SAAS,CAAC,QAAQ;AAChB,YAAI,QAAQ;AAAS,kBAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAA,MAAA,MAAA,SAAA,0BAAc,YAAY,GAAG;AAC7B,YAAI,QAAQ;AAAM,kBAAQ,KAAK,GAAG;AAAA,MACnC;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,SAAS,OAAO,SAAS,CAAA,GAAI,UAAU,CAAA,GAAI;AAChD,UAAM,MAAM,KAAK,SAAS,OAAO,MAAM;AAEvCA,kBAAAA,MAAI,SAAS;AAAA,MACX;AAAA,MACA,GAAG;AAAA,MACH,SAAS,CAAC,QAAQ;AAChB,YAAI,QAAQ;AAAS,kBAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAc,MAAA,MAAA,SAAA,0BAAA,WAAW,GAAG;AAC5B,YAAI,QAAQ;AAAM,kBAAQ,KAAK,GAAG;AAAA,MACnC;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,UAAU,OAAO,UAAU,IAAI;AACpCA,kBAAAA,MAAI,UAAU;AAAA,MACZ,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS,CAAC,QAAQ;AAChB,YAAI,QAAQ;AAAS,kBAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAA,MAAA,MAAA,SAAA,2BAAc,YAAY,GAAG;AAC7B,YAAI,QAAQ;AAAM,kBAAQ,KAAK,GAAG;AAAA,MACnC;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,aAAa,QAAQ,GAAG,UAAU,CAAA,GAAI;AAC3CA,kBAAAA,MAAI,aAAa;AAAA,MACf;AAAA,MACA,GAAG;AAAA,MACH,SAAS,CAAC,QAAQ;AAChB,YAAI,QAAQ;AAAS,kBAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAc,MAAA,MAAA,SAAA,2BAAA,WAAW,GAAG;AAC5B,YAAI,QAAQ;AAAM,kBAAQ,KAAK,GAAG;AAAA,MACnC;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,SAAS,OAAO,SAAS,IAAI;AAClC,QAAI,CAAC,UAAU,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AAC/C,aAAO;AAAA,IACR;AAED,UAAM,cAAc,OAAO,KAAK,MAAM,EACnC,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAC1E,KAAK,GAAG;AAEX,WAAO,GAAG,KAAK,IAAI,WAAW;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,YAAY,KAAK;AACtB,UAAM,SAAS,CAAA;AACf,UAAM,aAAa,IAAI,QAAQ,GAAG;AAElC,QAAI,eAAe,IAAI;AACrB,aAAO;AAAA,IACR;AAED,UAAM,cAAc,IAAI,UAAU,aAAa,CAAC;AAChD,UAAM,QAAQ,YAAY,MAAM,GAAG;AAEnC,UAAM,QAAQ,UAAQ;AACpB,YAAM,CAAC,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG;AACnC,UAAI,OAAO,OAAO;AAChB,eAAO,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK;AAAA,MAC3D;AAAA,IACP,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,UAAU,OAAO;AACtB,UAAM,WAAW,CAAC,OAAO,OAAO,OAAO,IAAI;AAC3C,WAAO,SAAS,SAAS,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,kBAAkB;AACvB,UAAM,QAAQ;AACd,QAAI,MAAM,SAAS,GAAG;AACpB,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,aAAO,IAAI,YAAY,KAAK;AAAA,IAC7B;AACD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,cAAc,OAAO,SAAS,CAAA,GAAI,UAAU,CAAA,GAAI;AACrD,QAAI,KAAK,UAAU,KAAK,GAAG;AACzB,WAAK,UAAU,OAAO,OAAO;AAAA,IACnC,OAAW;AACL,WAAK,WAAW,OAAO,QAAQ,OAAO;AAAA,IACvC;AAAA,EACF;AACH;AAKY,MAAC,WAAW;AAAA;AAAA,EAEtB,SAAS,MAAM,eAAe,UAAU,OAAO,KAAK;AAAA,EACpD,QAAQ,MAAM,eAAe,UAAU,OAAO,IAAI;AAAA;AAAA,EAGlD,SAAS,MAAM,eAAe,WAAW,OAAO,KAAK;AAAA,EACrD,YAAY,MAAM,eAAe,WAAW,OAAO,SAAS;AAAA;AAAA,EAG5D,QAAQ,MAAM,eAAe,WAAW,OAAO,IAAI;AAAA,EACnD,iBAAiB,MAAM,eAAe,WAAW,OAAO,cAAc;AAAA,EACtE,iBAAiB,MAAM,eAAe,WAAW,OAAO,cAAc;AAAA;AAAA,EAGtE,aAAa,MAAM,eAAe,WAAW,OAAO,UAAU;AAAA,EAC9D,eAAe,CAAC,OAAO,eAAe,WAAW,OAAO,cAAc,EAAE,IAAI;AAAA,EAC5E,YAAY,MAAM,eAAe,WAAW,OAAO,SAAS;AAAA,EAC5D,aAAa,CAAC,OAAO,eAAe,WAAW,OAAO,YAAY,EAAE,IAAI;AAAA;AAAA,EAGxE,MAAM,CAAC,QAAQ,MAAM,eAAe,aAAa,KAAK;AAAA,EACtD,OAAO,CAAC,OAAO,QAAQ,YAAY,eAAe,cAAc,OAAO,QAAQ,OAAO;AACxF;;"}