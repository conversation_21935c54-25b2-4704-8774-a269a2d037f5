"use strict";
const common_vendor = require("../common/vendor.js");
const ROUTES = {
  // 主要页面
  INDEX: "/pages/index/index",
  MINE: "/pages/mine/mine",
  // 登录相关
  LOGIN: "/pages/login/login",
  USER_INFO: "/pages/user-info/user-info",
  // 认证相关
  AUTH: "/pages/auth/auth",
  USER_AGREEMENT: "/pages/user_agreement/user_agreement",
  PRIVACY_POLICY: "/pages/privacy_policy/privacy_policy",
  // 业务页面
  ASSET_LIST: "/pages/asset/list",
  ASSET_DETAIL: "/pages/asset/detail",
  ASSET_ADD: "/pages/asset/add",
  ASSET_EDIT: "/pages/asset/edit",
  // 其他页面
  SETTINGS: "/pages/settings/settings",
  ABOUT: "/pages/about/about"
};
class RouteNavigator {
  /**
   * 跳转到指定页面
   * @param {string} route 路由路径
   * @param {Object} params 参数
   * @param {Object} options 跳转选项
   */
  static navigateTo(route, params = {}, options = {}) {
    const url = this.buildUrl(route, params);
    common_vendor.index.navigateTo({
      url,
      ...options,
      success: (res) => {
        if (options.success)
          options.success(res);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at config/routes.js:47", "页面跳转失败:", err);
        if (options.fail)
          options.fail(err);
      }
    });
  }
  /**
   * 重定向到指定页面
   * @param {string} route 路由路径
   * @param {Object} params 参数
   * @param {Object} options 跳转选项
   */
  static redirectTo(route, params = {}, options = {}) {
    const url = this.buildUrl(route, params);
    common_vendor.index.redirectTo({
      url,
      ...options,
      success: (res) => {
        if (options.success)
          options.success(res);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at config/routes.js:69", "页面重定向失败:", err);
        if (options.fail)
          options.fail(err);
      }
    });
  }
  /**
   * 重新启动到指定页面
   * @param {string} route 路由路径
   * @param {Object} params 参数
   * @param {Object} options 跳转选项
   */
  static reLaunch(route, params = {}, options = {}) {
    const url = this.buildUrl(route, params);
    common_vendor.index.reLaunch({
      url,
      ...options,
      success: (res) => {
        if (options.success)
          options.success(res);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at config/routes.js:91", "页面重启失败:", err);
        if (options.fail)
          options.fail(err);
      }
    });
  }
  /**
   * 切换到Tab页面
   * @param {string} route 路由路径
   * @param {Object} options 跳转选项
   */
  static switchTab(route, options = {}) {
    common_vendor.index.switchTab({
      url: route,
      ...options,
      success: (res) => {
        if (options.success)
          options.success(res);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at config/routes.js:110", "Tab切换失败:", err);
        if (options.fail)
          options.fail(err);
      }
    });
  }
  /**
   * 返回上一页
   * @param {number} delta 返回层数
   * @param {Object} options 选项
   */
  static navigateBack(delta = 1, options = {}) {
    common_vendor.index.navigateBack({
      delta,
      ...options,
      success: (res) => {
        if (options.success)
          options.success(res);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at config/routes.js:129", "页面返回失败:", err);
        if (options.fail)
          options.fail(err);
      }
    });
  }
  /**
   * 构建URL
   * @param {string} route 路由路径
   * @param {Object} params 参数
   * @returns {string} 完整URL
   */
  static buildUrl(route, params = {}) {
    if (!params || Object.keys(params).length === 0) {
      return route;
    }
    const queryString = Object.keys(params).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join("&");
    return `${route}?${queryString}`;
  }
  /**
   * 解析URL参数
   * @param {string} url URL字符串
   * @returns {Object} 参数对象
   */
  static parseParams(url) {
    const params = {};
    const queryIndex = url.indexOf("?");
    if (queryIndex === -1) {
      return params;
    }
    const queryString = url.substring(queryIndex + 1);
    const pairs = queryString.split("&");
    pairs.forEach((pair) => {
      const [key, value] = pair.split("=");
      if (key && value) {
        params[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    });
    return params;
  }
  /**
   * 检查是否为Tab页面
   * @param {string} route 路由路径
   * @returns {boolean} 是否为Tab页面
   */
  static isTabPage(route) {
    const tabPages = [ROUTES.INDEX, ROUTES.MINE];
    return tabPages.includes(route);
  }
  /**
   * 获取当前页面路径
   * @returns {string} 当前页面路径
   */
  static getCurrentRoute() {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      return `/${currentPage.route}`;
    }
    return "";
  }
  /**
   * 智能导航 - 根据页面类型自动选择导航方式
   * @param {string} route 路由路径
   * @param {Object} params 参数
   * @param {Object} options 选项
   */
  static smartNavigate(route, params = {}, options = {}) {
    if (this.isTabPage(route)) {
      this.switchTab(route, options);
    } else {
      this.navigateTo(route, params, options);
    }
  }
}
const navigate = {
  // 主要页面
  toIndex: () => RouteNavigator.switchTab(ROUTES.INDEX),
  toMine: () => RouteNavigator.switchTab(ROUTES.MINE),
  // 登录相关
  toLogin: () => RouteNavigator.navigateTo(ROUTES.LOGIN),
  toUserInfo: () => RouteNavigator.navigateTo(ROUTES.USER_INFO),
  // 认证相关
  toAuth: () => RouteNavigator.navigateTo(ROUTES.AUTH),
  toUserAgreement: () => RouteNavigator.navigateTo(ROUTES.USER_AGREEMENT),
  toPrivacyPolicy: () => RouteNavigator.navigateTo(ROUTES.PRIVACY_POLICY),
  // 资产相关
  toAssetList: () => RouteNavigator.navigateTo(ROUTES.ASSET_LIST),
  toAssetDetail: (id) => RouteNavigator.navigateTo(ROUTES.ASSET_DETAIL, { id }),
  toAssetAdd: () => RouteNavigator.navigateTo(ROUTES.ASSET_ADD),
  toAssetEdit: (id) => RouteNavigator.navigateTo(ROUTES.ASSET_EDIT, { id }),
  // 通用方法
  back: (delta = 1) => RouteNavigator.navigateBack(delta),
  smart: (route, params, options) => RouteNavigator.smartNavigate(route, params, options)
};
exports.navigate = navigate;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/routes.js.map
